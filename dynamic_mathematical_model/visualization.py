# """
# 可视化模块
# 提供各种数据可视化功能，包括传感器数据和模型结果的可视化
# """

# import numpy as np
# import matplotlib.pyplot as plt
# import matplotlib.tri as mtri
# from matplotlib import cm
# import seaborn as sns
# import os

# # 设置字体和样式（无需中文字体，但保留兼容性）
# plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
# plt.rcParams['axes.unicode_minus'] = False
# sns.set_style("whitegrid")

# class ModelVisualizer:
#     """模型可视化类"""
    
#     def __init__(self, figsize=(12, 8), dpi=300):
#         """
#         Initialize the visualizer
        
#         Args:
#             figsize (tuple): Default figure size
#             dpi (int): Figure resolution
#         """
#         self.figsize = figsize
#         self.dpi = dpi
#         self.color_maps = {
#             'temperature': 'coolwarm',
#             'ph': 'RdYlBu_r',
#             'conductivity': 'viridis',
#             'chlorophyll': 'Greens',
#             'health': 'RdYlGn'
#         }
    
#     def create_directory(self, path):
#         """Create directory if it doesn't exist"""
#         if not os.path.exists(path):
#             os.makedirs(path)

#     # visualization.py in class ModelVisualizer

#     def plot_sensor_data_2d(self, node_coords, sensor_data, elements, save_path=None):
#         """
#         Plot 2D surface plots of sensor data
        
#         Args:
#             node_coords (np.array): Node coordinates (N, 3)
#             sensor_data (dict): Sensor data
#             elements (np.array): Triangle elements (M, 3)
#             save_path (str): Save path
#         """
#         # 创建三角化对象（仅使用 X, Y 坐标）
#         # 注意：gmsh的单元是从1开始索引的，python是从0开始，所以要减1
#         triang = mtri.Triangulation(node_coords[:, 0], node_coords[:, 1], elements[:, :3] - 1)
        
#         fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
#         parameters = ['temperature', 'ph', 'conductivity', 'chlorophyll']
#         titles = ['Temperature Distribution (°C)', 'pH Distribution', 
#                  'Conductivity Distribution (μS/cm)', 'Chlorophyll Distribution (μg/L)']
        
#         for i, (param, title) in enumerate(zip(parameters, titles)):
#             ax = axes[i//2, i%2]
            
#             # --- 核心修改在这里 ---
#             # 旧代码:
#             # tcf = ax.tripcolor(triang, sensor_data[param], cmap=self.color_maps[param], shading='gouraud')
            
#             # 新代码: 使用 tricontourf 实现平滑填充
#             # levels=100 参数让颜色过渡更平滑
#             tcf = ax.tricontourf(triang, sensor_data[param], levels=100, cmap=self.color_maps[param])
#             # ---------------------

#             ax.set_xlabel('X Coordinate (m)')
#             ax.set_ylabel('Y Coordinate (m)')
#             ax.set_title(title)
#             ax.set_aspect('equal')
            
#             # 添加颜色条
#             cbar = plt.colorbar(tcf, ax=ax)
#             cbar.set_label(param.capitalize())
            
#             # 添加统计信息
#             data = sensor_data[param]
#             stats_text = f'Mean: {np.mean(data):.2f}\nStandard Deviation: {np.std(data):.2f}'
#             ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
#                    verticalalignment='top', bbox=dict(boxstyle='round', 
#                    facecolor='white', alpha=0.8))
        
#         plt.tight_layout()
        
#         if save_path:
#             self.create_directory(save_path)
#             plt.savefig(f'{save_path}/sensor_data_2d.png', 
#                        dpi=self.dpi, bbox_inches='tight')
#         plt.show()
    
    

#     # visualization.py in class ModelVisualizer

#     def plot_health_index_2d(self, node_coords, health_index, elements, save_path=None):
#         """
#         Plot 2D surface plot of health index
        
#         Args:
#             node_coords (np.array): Node coordinates (N, 3)
#             health_index (np.array): Health index
#             elements (np.array): Triangle elements (M, 3)
#             save_path (str): Save path
#         """
#         # 创建三角化对象
#         triang = mtri.Triangulation(node_coords[:, 0], node_coords[:, 1], elements[:, :3] - 1)
        
#         fig, ax = plt.subplots(figsize=self.figsize)
        
#         # --- 核心修改在这里 ---
#         # 旧代码:
#         # tcf = ax.tripcolor(triang, health_index, cmap=self.color_maps['health'], 
#         #                   shading='gouraud', vmin=0, vmax=1)

#         # 新代码: 使用 tricontourf 实现平滑填充
#         tcf = ax.tricontourf(triang, health_index, levels=150, cmap=self.color_maps['health'], 
#                              vmin=0, vmax=1)
#         # ---------------------

#         ax.set_xlabel('X Coordinate (m)')
#         ax.set_ylabel('Y Coordinate (m)')
#         ax.set_title('Pearl Mussel Health Index Distribution')
#         ax.set_aspect('equal')
        
#         # 添加颜色条
#         cbar = plt.colorbar(tcf, ax=ax)
#         cbar.set_label('Health Index')
        
#         # 添加统计信息
#         stats_text = f'Mean: {np.mean(health_index):.3f}\n' \
#                     f'Standard Deviation: {np.std(health_index):.3f}\n' \
#                     f'Minimum: {np.min(health_index):.3f}\n' \
#                     f'Maximum: {np.max(health_index):.3f}'
#         ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
#                verticalalignment='top', bbox=dict(boxstyle='round', 
#                facecolor='white', alpha=0.9))
        
#         plt.tight_layout()
        
#         if save_path:
#             self.create_directory(save_path)
#             plt.savefig(f'{save_path}/health_index_2d.png', 
#                        dpi=self.dpi, bbox_inches='tight')
#         plt.show()
    
    
#     def plot_3d_visualization(self, node_coords, data, title, colormap, save_path=None):
#         """
#         Create 3D scatter plot visualization
        
#         Args:
#             node_coords (np.array): Node coordinates
#             data (np.array): Data to visualize
#             title (str): Figure title
#             colormap (str): Colormap
#             save_path (str): Save path
#         """
#         fig = plt.figure(figsize=(12, 9))
#         ax = fig.add_subplot(111, projection='3d')
        
#         scatter = ax.scatter(node_coords[:, 0], node_coords[:, 1], node_coords[:, 2],
#                            c=data, cmap=colormap, s=30, alpha=0.6)
        
#         ax.set_xlabel('X Coordinate (m)')
#         ax.set_ylabel('Y Coordinate (m)')
#         ax.set_zlabel('Z Coordinate (m)')
#         ax.set_title('Pearl Mussel Health Index 3D Distribution')
        
#         # 添加颜色条
#         cbar = plt.colorbar(scatter, ax=ax, shrink=0.5, aspect=20)
#         cbar.set_label('Health Index')
        
#         plt.tight_layout()
        
#         if save_path:
#             self.create_directory(save_path)
#             filename = title.replace(' ', '_').replace('(', '').replace(')', '').lower()
#             plt.savefig(f'{save_path}/{filename}_3d.png', 
#                        dpi=self.dpi, bbox_inches='tight')
#         plt.show()
    
#     def plot_correlation_matrix(self, sensor_data, health_index, save_path=None):
#         """
#         Plot correlation matrix of parameters
        
#         Args:
#             sensor_data (dict): Sensor data
#             health_index (np.array): Health index
#             save_path (str): Save path
#         """
#         # 准备数据
#         data_dict = sensor_data.copy()
#         data_dict['health_index'] = health_index
        
#         # 创建DataFrame用于相关性分析
#         import pandas as pd
#         df = pd.DataFrame({
#             'Temperature': data_dict['temperature'],
#             'pH': data_dict['ph'],
#             'Conductivity': data_dict['conductivity'],
#             'Chlorophyll': data_dict['chlorophyll'],
#             'Health Index': data_dict['health_index']
#         })
        
#         # 计算相关性矩阵
#         correlation_matrix = df.corr()
        
#         # 绘制热力图
#         fig, ax = plt.subplots(figsize=(10, 8))
#         sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
#                    square=True, ax=ax, cbar_kws={'label': 'Correlation Coefficient'})
#         ax.set_title('Correlation Analysis of Environmental Parameters and Health Index')
        
#         plt.tight_layout()
        
#         if save_path:
#             self.create_directory(save_path)
#             plt.savefig(f'{save_path}/correlation_matrix.png', 
#                        dpi=self.dpi, bbox_inches='tight')
#         plt.show()
    
#     def plot_parameter_distributions(self, sensor_data, health_index, save_path=None):
#         """
#         Plot histogram of parameter distributions
        
#         Args:
#             sensor_data (dict): Sensor data
#             health_index (np.array): Health index
#             save_path (str): Save path
#         """
#         fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
#         parameters = ['temperature', 'ph', 'conductivity', 'chlorophyll']
#         titles = ['Temperature (°C)', 'pH', 'Conductivity (μS/cm)', 'Chlorophyll (μg/L)']
        
#         # 绘制传感器数据分布
#         for i, (param, title) in enumerate(zip(parameters, titles)):
#             ax = axes[i//2, i%2]
#             data = sensor_data[param]
            
#             ax.hist(data, bins=30, alpha=0.7, edgecolor='black', 
#                    color=plt.cm.Set3(i))
#             ax.set_title(f'{title} Distribution')
#             ax.set_xlabel(title)
#             ax.set_ylabel('Frequency')
#             ax.grid(True, alpha=0.3)
            
#             # 添加统计线
#             mean_val = np.mean(data)
#             ax.axvline(mean_val, color='red', linestyle='--', 
#                       label=f'Mean: {mean_val:.2f}')
#             ax.legend()
        
#         # 绘制健康度指数分布
#         ax = axes[1, 2]
#         ax.hist(health_index, bins=30, alpha=0.7, edgecolor='black', 
#                color='lightgreen')
#         ax.set_title('Health Index Distribution')
#         ax.set_xlabel('Health Index')
#         ax.set_ylabel('Frequency')
#         ax.grid(True, alpha=0.3)
        
#         mean_health = np.mean(health_index)
#         ax.axvline(mean_health, color='red', linestyle='--', 
#                   label=f'Mean: {mean_health:.3f}')
#         ax.legend()
        
#         plt.tight_layout()
        
#         if save_path:
#             self.create_directory(save_path)
#             plt.savefig(f'{save_path}/parameter_distributions.png', 
#                        dpi=self.dpi, bbox_inches='tight')
#         plt.show()
    
#     # def create_comprehensive_visualization(self, node_coords, sensor_data, health_index, elements, save_path=None):
#     def create_comprehensive_visualization(self, node_coords, sensor_data, health_index=None, elements=None, save_path='results', is_prediction=False):
#     # ... 函数内容 ...
#         """
#         Create comprehensive visualization report
        
#         Args:
#             node_coords (np.array): Node coordinates
#             sensor_data (dict): Sensor data
#             health_index (np.array): Health index
#             elements (np.array): Triangle elements
#             save_path (str): Save path
#         """
#         print("Generating 2D sensor data surface plots...")
#         self.plot_sensor_data_2d(node_coords, sensor_data, elements, save_path)
        
#         print("Generating 2D health index surface plot...")
#         self.plot_health_index_2d(node_coords, health_index, elements, save_path)
        
#         print("Generating parameter distribution histograms...")
#         self.plot_parameter_distributions(sensor_data, health_index, save_path)
        
#         print("Generating correlation analysis plot...")
#         self.plot_correlation_matrix(sensor_data, health_index, save_path)
        
#         # 如果有3D坐标，生成3D可视化
#         if node_coords.shape[1] > 2:
#             print("Generating 3D scatter visualization...")
#             self.plot_3d_visualization(node_coords, health_index, 
#                                      'Pearl Mussel Health Index 3D Distribution', 
#                                      self.color_maps['health'], save_path)
        
#         print("✅ All visualization plots generated successfully!")
    
#     def plot_time_series(self, time_data, health_data, title="Health Index Time Series", save_path=None):
#         """
#         Plot time series
        
#         Args:
#             time_data (np.array): Time data
#             health_data (np.array): Health data
#             title (str): Figure title
#             save_path (str): Save path
#         """
#         fig, ax = plt.subplots(figsize=self.figsize)
        
#         ax.plot(time_data, health_data, linewidth=2, color='blue')
#         ax.set_xlabel('Time (days)')
#         ax.set_ylabel('Health Index')
#         ax.set_title(title)
#         ax.grid(True, alpha=0.3)
        
#         plt.tight_layout()
        
#         if save_path:
#             self.create_directory(save_path)
#             plt.savefig(f'{save_path}/time_series.png', 
#                        dpi=self.dpi, bbox_inches='tight')
#         plt.show()






# dynamic_mathematical_model/visualization.py

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.tri as mtri
import seaborn as sns
import pandas as pd
import os

# 设置全局样式
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

class ModelVisualizer:
    """负责所有与模型相关的可视化任务"""
    
    def __init__(self, dpi=150):
        self.dpi = dpi
        self.color_maps = {
            'temperature': 'coolwarm',
            'ph': 'RdYlBu_r',
            'conductivity': 'viridis',
            'chlorophyll': 'Greens',
            'health': 'RdYlGn' # 用于健康度
        }

    def _create_directory(self, path):
        """如果目录不存在则创建"""
        if not os.path.exists(path):
            os.makedirs(path)

    def _get_prefix(self, is_prediction):
        """根据模式返回文件名前缀"""
        return "prediction_" if is_prediction else ""

    def plot_sensor_data_2d(self, node_coords, sensor_data, elements, save_path, is_prediction):
        """绘制2D传感器数据热力图"""
        prefix = self._get_prefix(is_prediction)
        triang = mtri.Triangulation(node_coords[:, 0], node_coords[:, 1], elements[:, :3] - 1)
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        parameters = list(sensor_data.keys())
        titles = [f'{p.capitalize()} Distribution' for p in parameters]

        for i, (param, title) in enumerate(zip(parameters, titles)):
            ax = axes[i//2, i%2]
            tcf = ax.tricontourf(triang, sensor_data[param], levels=100, cmap=self.color_maps[param])
            ax.set_xlabel('X Coordinate (m)'); ax.set_ylabel('Y Coordinate (m)')
            ax.set_title(title); ax.set_aspect('equal')
            cbar = plt.colorbar(tcf, ax=ax); cbar.set_label(param.capitalize())

        plt.tight_layout()
        plt.savefig(os.path.join(save_path, f'{prefix}sensor_data_2d.png'), dpi=self.dpi)
        plt.close()

    def plot_health_index_2d(self, node_coords, health_index, elements, save_path, is_prediction):
        """绘制2D健康度指数热力图"""
        prefix = self._get_prefix(is_prediction)
        triang = mtri.Triangulation(node_coords[:, 0], node_coords[:, 1], elements[:, :3] - 1)
        
        plt.figure(figsize=(8, 6))
        tcf = plt.tricontourf(triang, health_index, levels=150, cmap=self.color_maps['health'], vmin=0, vmax=1)
        plt.xlabel('X Coordinate (m)'); plt.ylabel('Y Coordinate (m)')
        plt.title('Pearl Mussel Health Index Distribution')
        plt.gca().set_aspect('equal')
        cbar = plt.colorbar(tcf); cbar.set_label('Health Index (u)')

        plt.tight_layout()
        plt.savefig(os.path.join(save_path, f'{prefix}health_index_2d.png'), dpi=self.dpi)
        plt.close()

    def plot_3d_visualization(self, node_coords, health_index, save_path, is_prediction):
        """创建3D健康度散点图"""
        prefix = self._get_prefix(is_prediction)
        fig = plt.figure(figsize=(12, 9))
        ax = fig.add_subplot(111, projection='3d')
        
        scatter = ax.scatter(node_coords[:, 0], node_coords[:, 1], node_coords[:, 2],
                           c=health_index, cmap=self.color_maps['health'], s=20, alpha=0.7, vmin=0, vmax=1)
        
        ax.set_xlabel('X (m)'); ax.set_ylabel('Y (m)'); ax.set_zlabel('Z (m)')
        ax.set_title('3D Health Index Distribution')
        cbar = plt.colorbar(scatter, ax=ax, shrink=0.6); cbar.set_label('Health Index (u)')
        
        plt.savefig(os.path.join(save_path, f'{prefix}health_index_3d.png'), dpi=self.dpi)
        plt.close()

    def plot_correlation_matrix(self, sensor_data, health_index, save_path, is_prediction):
        """绘制参数相关性矩阵热力图"""
        prefix = self._get_prefix(is_prediction)
        df_data = sensor_data.copy()
        df_data['Health Index'] = health_index
        df = pd.DataFrame(df_data)
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(df.corr(), annot=True, cmap='coolwarm', center=0, square=True)
        plt.title('Correlation Matrix of Parameters and Health Index')
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_path, f'{prefix}correlation_matrix.png'), dpi=self.dpi)
        plt.close()

    def plot_parameter_distributions(self, sensor_data, health_index, save_path, is_prediction):
        """绘制参数分布直方图"""
        prefix = self._get_prefix(is_prediction)
        fig, axes = plt.subplots(2, 3, figsize=(18, 10))
        all_data = sensor_data.copy()
        all_data['Health Index'] = health_index
        
        for i, (param, data) in enumerate(all_data.items()):
            ax = axes.flatten()[i]
            sns.histplot(data, bins=30, ax=ax, kde=True)
            ax.set_title(f'{param.capitalize()} Distribution')
            ax.axvline(np.mean(data), color='red', linestyle='--', label=f'Mean: {np.mean(data):.2f}')
            ax.legend()
        
        # 隐藏多余的子图（如果有的话）
        for j in range(len(all_data), len(axes.flatten())):
            axes.flatten()[j].set_visible(False)
            
        plt.tight_layout()
        plt.savefig(os.path.join(save_path, f'{prefix}parameter_distributions.png'), dpi=self.dpi)
        plt.close()

    def create_comprehensive_visualization(self, node_coords, sensor_data, health_index=None, elements=None, save_path='results', is_prediction=False):
        """
        创建一套完整的可视化图表。
        会智能检查传入的数据来决定绘制哪些图。
        """
        self._create_directory(save_path)
        
        # --- 智能检查逻辑 ---
        
        # 只有当sensor_data字典不为空时，才绘制传感器数据图
        if sensor_data:
            print("  > Generating 2D sensor data plots...")
            self.plot_sensor_data_2d(node_coords, sensor_data, elements, save_path, is_prediction)
        
        # 只有当health_index被传入时，才绘制健康度图
        if health_index is not None:
            print("  > Generating health index plots (2D and 3D)...")
            self.plot_health_index_2d(node_coords, health_index, elements, save_path, is_prediction)
            if node_coords.shape[1] > 2: # 检查是否有Z坐标
                self.plot_3d_visualization(node_coords, health_index, save_path, is_prediction)
        
        # 只有当两者都存在时，才能进行综合分析
        if sensor_data and health_index is not None:
            print("  > Generating comprehensive analysis plots (Distributions, Correlation)...")
            self.plot_parameter_distributions(sensor_data, health_index, save_path, is_prediction)
            self.plot_correlation_matrix(sensor_data, health_index, save_path, is_prediction)

        print(f"✅ Visualization charts saved to '{save_path}'")