# python "dynamic mathematical model/pearl_mussel_model.py"
# dynamic mathematical model/pearl_mussel_model.py

import sys
import os
current_file_path = os.path.abspath(__file__)
current_dir = os.path.dirname(current_file_path)
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)
import numpy as np
from shared_utils.mesh_loader import load_msh_data
from data_generator import SensorDataGenerator
from visualization import ModelVisualizer
from model_core import PearlMusselModel


def main():
    """主函数：协调各个模块完成完整的建模流程"""
    print("--- 开始执行珍珠蚌健康度模型 ---")

    # --- 1. 加载网格数据 ---
    # 使用共享的加载器，不再在此文件中直接调用gmsh
    # 路径相对于项目根目录
    mesh_path = os.path.join('data', 'mesh', 'lake_box_mesh_detailed.msh')
    print(f"正在从 '{mesh_path}' 加载网格...")
    
    node_coords, elements = load_msh_data(mesh_path)

    # 关键的错误检查：如果网格加载失败，则终止程序
    if node_coords is None:
        print("❌ 无法继续执行，因为网格加载失败。请检查文件路径或文件内容。")
        return

    # --- 2. 初始化模型参数 ---
    num_nodes = node_coords.shape[0]
    t_span = [0, 30]  # 时间跨度：30天
    u0 = np.full(num_nodes, 0.5)  # 初始健康度指数
    k = 1.0  # 环境承载力
    print(f"模型参数初始化完成，网格节点数量: {num_nodes}")

    # --- 3. 生成传感器模拟数据 ---
    print("\n--- 步骤 1/3: 正在生成传感器数据... ---")
    data_generator = SensorDataGenerator(seed=42)
    sensor_data = data_generator.generate_realistic_data(
        node_coords=node_coords,
        num_nodes=num_nodes
    )
    # 保存传感器数据
    data_generator.save_sensor_data(sensor_data, 'sensor_data.npz')
    print("✅ 传感器数据已生成并保存至 'sensor_data.npz'")

    # --- 4. 运行核心动力学模型 ---
    print("\n--- 步骤 2/3: 正在运行核心动力学模型... ---")
    model = PearlMusselModel()

    # 向量化计算，提升效率
    # 计算所有节点的生长率 r
    r = model.ideal_growth_rate(sensor_data['temperature'], sensor_data['chlorophyll'])
    
    # 计算所有节点的综合胁迫因子 sigma_total
    sigma_total = model.total_stress(sensor_data['ph'], sensor_data['conductivity'])

    # 求解每个节点的ODE以获得最终健康度
    u_final = np.zeros(num_nodes)
    for i in range(num_nodes):
        # 假定模型达到稳态或取最终时刻的值
        _, u = model.solve_ode(u0[i], t_span, r[i], sigma_total[i], k)
        u_final[i] = u[-1]  # 取最终时间步的健康度

    print("✅ 模型计算完成")
    
    # 保存最终结果
    results_dir = 'results'
    if not os.path.exists(results_dir):
        os.makedirs(results_dir)
    health_index_path = os.path.join(results_dir, 'health_index.txt')
    np.savetxt(health_index_path, np.column_stack((node_coords, u_final)),
               header='x y z health_index', fmt='%.6f')
    print(f"✅ 健康度指数已保存至 '{health_index_path}'")

    # --- 5. 可视化结果 ---
    print("\n--- 步骤 3/3: 正在生成可视化图表... ---")
    visualizer = ModelVisualizer()

    # 创建并保存一系列可视化图表
    visualizer.create_comprehensive_visualization(
        node_coords=node_coords,
        sensor_data=sensor_data,
        health_index=u_final,
        elements=elements,
        save_path=results_dir
    )
    print(f"✅ 所有可视化图表已保存至 '{results_dir}' 目录")
    print("\n--- 模型运行流程全部完成 ---")

if __name__ == "__main__":
    main()