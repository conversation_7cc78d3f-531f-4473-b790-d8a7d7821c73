#!/usr/bin/env python3
"""
叶绿素爆发生长模型分析
详细分析和可视化叶绿素爆发的数学模型
"""

import numpy as np
import matplotlib.pyplot as plt
import os

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def analyze_sigmoid_growth_function():
    """分析Sigmoid生长函数的特性"""
    
    # 时间进展比例 (0 到 1)
    time_progress = np.linspace(0, 1, 100)
    
    # 不同参数的Sigmoid函数
    sigmoid_configs = [
        {'k': 10, 'x0': 0.6, 'label': '标准配置 (k=10, x0=0.6)', 'color': 'red'},
        {'k': 5, 'x0': 0.6, 'label': '缓慢增长 (k=5, x0=0.6)', 'color': 'blue'},
        {'k': 15, 'x0': 0.6, 'label': '快速增长 (k=15, x0=0.6)', 'color': 'green'},
        {'k': 10, 'x0': 0.4, 'label': '早期爆发 (k=10, x0=0.4)', 'color': 'orange'},
        {'k': 10, 'x0': 0.8, 'label': '晚期爆发 (k=10, x0=0.8)', 'color': 'purple'}
    ]
    
    # 叶绿素参数
    initial_mean = 8.0  # μg/L
    peak_mean = 85.0    # μg/L
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('叶绿素爆发生长模型分析', fontsize=16, fontweight='bold')
    
    # 1. Sigmoid函数形状对比
    for config in sigmoid_configs:
        k, x0 = config['k'], config['x0']
        sigmoid_factor = 1 / (1 + np.exp(-k * (time_progress - x0)))
        ax1.plot(time_progress, sigmoid_factor, 
                label=config['label'], color=config['color'], linewidth=2)
    
    ax1.set_title('Sigmoid函数形状对比', fontsize=14, fontweight='bold')
    ax1.set_xlabel('时间进展比例')
    ax1.set_ylabel('Sigmoid因子')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    ax1.set_xlim(0, 1)
    ax1.set_ylim(0, 1)
    
    # 添加关键点标注
    ax1.axhline(y=0.5, color='gray', linestyle='--', alpha=0.5)
    ax1.axvline(x=0.6, color='gray', linestyle='--', alpha=0.5)
    ax1.text(0.6, 0.5, '拐点 (0.6, 0.5)', fontsize=10, 
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # 2. 叶绿素浓度随时间变化
    standard_sigmoid = 1 / (1 + np.exp(-10 * (time_progress - 0.6)))
    chlorophyll_concentration = initial_mean + (peak_mean - initial_mean) * standard_sigmoid
    
    ax2.plot(time_progress * 30, chlorophyll_concentration, 'red', linewidth=3, label='叶绿素浓度')
    ax2.axhline(y=30, color='orange', linestyle='--', linewidth=2, label='危险阈值 (30 μg/L)')
    ax2.axhline(y=initial_mean, color='green', linestyle='--', alpha=0.7, label=f'初始浓度 ({initial_mean} μg/L)')
    ax2.axhline(y=peak_mean, color='darkred', linestyle='--', alpha=0.7, label=f'峰值浓度 ({peak_mean} μg/L)')
    
    ax2.set_title('叶绿素浓度时间序列', fontsize=14, fontweight='bold')
    ax2.set_xlabel('时间步 (天)')
    ax2.set_ylabel('叶绿素浓度 (μg/L)')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    # 标注关键阶段
    ax2.fill_between([0, 18], 0, 120, alpha=0.1, color='green', label='缓慢增长期')
    ax2.fill_between([18, 24], 0, 120, alpha=0.1, color='orange', label='快速爆发期')
    ax2.fill_between([24, 30], 0, 120, alpha=0.1, color='red', label='稳定期')
    
    # 3. 增长速率分析
    # 计算一阶导数 (增长速率)
    dt = time_progress[1] - time_progress[0]
    growth_rate = np.gradient(chlorophyll_concentration, dt)
    
    ax3.plot(time_progress * 30, growth_rate, 'blue', linewidth=3)
    ax3.set_title('叶绿素增长速率', fontsize=14, fontweight='bold')
    ax3.set_xlabel('时间步 (天)')
    ax3.set_ylabel('增长速率 (μg/L/天)')
    ax3.grid(True, alpha=0.3)
    
    # 找到最大增长速率点
    max_rate_idx = np.argmax(growth_rate)
    max_rate_time = time_progress[max_rate_idx] * 30
    max_rate_value = growth_rate[max_rate_idx]
    
    ax3.plot(max_rate_time, max_rate_value, 'ro', markersize=10)
    ax3.text(max_rate_time, max_rate_value + 0.5, 
             f'最大增长速率\n({max_rate_time:.1f}天, {max_rate_value:.2f}μg/L/天)',
             fontsize=10, ha='center',
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # 4. 空间热点效应模拟
    # 模拟距离对热点强度的影响
    distances = np.linspace(0, 1, 100)  # 归一化距离
    hotspot_intensity = 2.5
    current_mean_example = 50.0  # 示例浓度
    sigmoid_factor_example = 0.8  # 示例Sigmoid因子
    
    hotspot_effect = (hotspot_intensity * current_mean_example * 
                     np.exp(-5 * distances) * sigmoid_factor_example)
    
    ax4.plot(distances, hotspot_effect, 'green', linewidth=3, label='热点效应强度')
    ax4.set_title('空间热点效应衰减', fontsize=14, fontweight='bold')
    ax4.set_xlabel('距离热点中心的归一化距离')
    ax4.set_ylabel('额外叶绿素浓度 (μg/L)')
    ax4.grid(True, alpha=0.3)
    ax4.legend()
    
    # 添加衰减特性说明
    ax4.text(0.5, max(hotspot_effect) * 0.7, 
             '指数衰减函数:\neffect = I × C × exp(-5d) × S\n\n'
             'I: 热点强度倍数 (2.5)\n'
             'C: 当前平均浓度\n'
             'd: 归一化距离\n'
             'S: Sigmoid时间因子',
             fontsize=10,
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图表
    save_dir = 'results/growth_model_analysis'
    os.makedirs(save_dir, exist_ok=True)
    plt.savefig(os.path.join(save_dir, 'chlorophyll_growth_model_analysis.png'), 
                dpi=300, bbox_inches='tight')
    print(f"✅ 生长模型分析图已保存: {os.path.join(save_dir, 'chlorophyll_growth_model_analysis.png')}")
    plt.close()

def create_mathematical_formula_explanation():
    """创建数学公式说明"""
    
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    ax.axis('off')
    
    # 标题
    ax.text(0.5, 0.95, '叶绿素爆发数学模型', fontsize=20, fontweight='bold', 
            ha='center', transform=ax.transAxes)
    
    # 核心公式
    formulas = [
        "1. 时间进展比例:",
        "   t_progress = timestep / (total_timesteps - 1)",
        "",
        "2. Sigmoid生长函数:",
        "   S(t) = 1 / (1 + exp(-k × (t_progress - x₀)))",
        "   其中: k = 10 (增长陡峭度), x₀ = 0.6 (拐点位置)",
        "",
        "3. 叶绿素基础浓度:",
        "   C_base(t) = C_initial + (C_peak - C_initial) × S(t)",
        "   其中: C_initial = 8.0 μg/L, C_peak = 85.0 μg/L",
        "",
        "4. 空间热点效应:",
        "   H_effect(d,t) = I × C_base(t) × exp(-α × d) × S(t)",
        "   其中: I = 2.5 (热点强度), α = 5 (衰减系数), d = 归一化距离",
        "",
        "5. 最终叶绿素浓度:",
        "   C_final = C_base + Σ H_effect + noise",
        "   其中: noise ~ N(0, 0.2 × C_base) (高斯噪声)",
        "",
        "6. 健康度影响:",
        "   额外压力 = 0.02 × max(0, C_final - 30)",
        "   当叶绿素 > 30 μg/L 时开始产生额外压力"
    ]
    
    y_pos = 0.85
    for formula in formulas:
        if formula.startswith(("1.", "2.", "3.", "4.", "5.", "6.")):
            ax.text(0.05, y_pos, formula, fontsize=14, fontweight='bold', 
                   transform=ax.transAxes)
        else:
            ax.text(0.05, y_pos, formula, fontsize=12, 
                   transform=ax.transAxes, family='monospace')
        y_pos -= 0.04
    
    # 添加模型特点说明
    ax.text(0.05, 0.25, "模型特点:", fontsize=16, fontweight='bold', 
           transform=ax.transAxes)
    
    features = [
        "• S型增长曲线: 模拟藻类爆发的自然过程",
        "• 空间异质性: 通过热点模拟营养物质聚集",
        "• 时空耦合: 热点效应随时间动态变化",
        "• 随机噪声: 模拟环境的不确定性",
        "• 生物学约束: 基于真实的生态阈值"
    ]
    
    y_pos = 0.20
    for feature in features:
        ax.text(0.05, y_pos, feature, fontsize=12, transform=ax.transAxes)
        y_pos -= 0.03
    
    plt.tight_layout()
    
    # 保存公式说明
    save_dir = 'results/growth_model_analysis'
    os.makedirs(save_dir, exist_ok=True)
    plt.savefig(os.path.join(save_dir, 'mathematical_formulas.png'), 
                dpi=300, bbox_inches='tight')
    print(f"✅ 数学公式说明已保存: {os.path.join(save_dir, 'mathematical_formulas.png')}")
    plt.close()

def compare_growth_models():
    """对比不同生长模型"""
    
    time_steps = np.arange(30)
    time_progress = time_steps / 29
    
    # 不同的生长模型
    models = {
        'Sigmoid (当前模型)': {
            'func': lambda t: 1 / (1 + np.exp(-10 * (t - 0.6))),
            'color': 'red',
            'description': 'S型曲线，模拟自然爆发过程'
        },
        'Exponential': {
            'func': lambda t: np.exp(3 * t) / np.exp(3),
            'color': 'blue', 
            'description': '指数增长，增长过快'
        },
        'Linear': {
            'func': lambda t: t,
            'color': 'green',
            'description': '线性增长，不符合生物规律'
        },
        'Logistic': {
            'func': lambda t: 1 / (1 + np.exp(-8 * (t - 0.5))),
            'color': 'orange',
            'description': 'Logistic增长，较为平缓'
        }
    }
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    # 增长因子对比
    for name, model in models.items():
        growth_factor = model['func'](time_progress)
        ax1.plot(time_steps, growth_factor, color=model['color'], 
                linewidth=3, label=name)
    
    ax1.set_title('不同生长模型对比', fontsize=14, fontweight='bold')
    ax1.set_xlabel('时间步 (天)')
    ax1.set_ylabel('增长因子')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    ax1.set_xlim(0, 29)
    ax1.set_ylim(0, 1)
    
    # 叶绿素浓度对比
    initial_mean, peak_mean = 8.0, 85.0
    for name, model in models.items():
        growth_factor = model['func'](time_progress)
        concentration = initial_mean + (peak_mean - initial_mean) * growth_factor
        ax2.plot(time_steps, concentration, color=model['color'], 
                linewidth=3, label=name)
    
    ax2.axhline(y=30, color='black', linestyle='--', alpha=0.7, label='危险阈值')
    ax2.set_title('叶绿素浓度对比', fontsize=14, fontweight='bold')
    ax2.set_xlabel('时间步 (天)')
    ax2.set_ylabel('叶绿素浓度 (μg/L)')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    plt.tight_layout()
    
    # 保存对比图
    save_dir = 'results/growth_model_analysis'
    os.makedirs(save_dir, exist_ok=True)
    plt.savefig(os.path.join(save_dir, 'growth_models_comparison.png'), 
                dpi=300, bbox_inches='tight')
    print(f"✅ 生长模型对比图已保存: {os.path.join(save_dir, 'growth_models_comparison.png')}")
    plt.close()

def main():
    """主函数"""
    print("=== 叶绿素爆发生长模型分析 ===")
    
    # 1. 分析Sigmoid生长函数
    print("1. 分析Sigmoid生长函数特性...")
    analyze_sigmoid_growth_function()
    
    # 2. 创建数学公式说明
    print("2. 创建数学公式说明...")
    create_mathematical_formula_explanation()
    
    # 3. 对比不同生长模型
    print("3. 对比不同生长模型...")
    compare_growth_models()
    
    print("\n✅ 所有分析完成！")
    print("生成的文件:")
    print("- chlorophyll_growth_model_analysis.png: 详细的模型分析")
    print("- mathematical_formulas.png: 数学公式说明")
    print("- growth_models_comparison.png: 不同模型对比")

if __name__ == '__main__':
    main()
