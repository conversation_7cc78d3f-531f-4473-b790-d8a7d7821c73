#!/usr/bin/env python3
"""
健康度动态退化可视化
展示珍珠养殖水域健康度从绿色（健康）到红色（不健康）的动态变化过程
配合靶向复核模式的路径优化算法
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
import matplotlib.tri as mtri
from matplotlib.colors import LinearSegmentedColormap
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.getcwd())
from shared_utils.mesh_loader import load_msh_data

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class HealthDegradationAnimator:
    """健康度退化动画器"""
    
    def __init__(self, node_coords, elements):
        self.node_coords = node_coords
        self.elements = elements
        self.x = node_coords[:, 0]
        self.y = node_coords[:, 1]
        
        # 创建三角化
        self.triang = mtri.Triangulation(self.x, self.y, elements[:, :3] - 1)
        
        # 定义问题区域（污染源）
        self.pollution_sources = [
            {'center': (np.mean(self.x) + 0.3 * (self.x.max() - self.x.min()), 
                       np.mean(self.y) + 0.2 * (self.y.max() - self.y.min())), 
             'intensity': 0.8, 'spread_rate': 0.15},
            {'center': (np.mean(self.x) - 0.2 * (self.x.max() - self.x.min()), 
                       np.mean(self.y) - 0.3 * (self.y.max() - self.y.min())), 
             'intensity': 0.6, 'spread_rate': 0.12},
            {'center': (np.mean(self.x) + 0.1 * (self.x.max() - self.x.min()), 
                       np.mean(self.y) - 0.1 * (self.y.max() - self.y.min())), 
             'intensity': 0.7, 'spread_rate': 0.18}
        ]
        
        # 创建自定义颜色映射：红色（不健康）到绿色（健康）
        # 健康度从0到1：红色→橙色→黄色→黄绿色→绿色
        colors = ['#FF0000', '#FF4500', '#FF8C00', '#FFD700', '#ADFF2F', '#00FF00']  # 红→橙→橙黄→金黄→黄绿→绿
        self.health_cmap = LinearSegmentedColormap.from_list('health', colors, N=256)
        
        # 计算域的特征尺寸
        self.domain_size = np.sqrt((self.x.max() - self.x.min())**2 + (self.y.max() - self.y.min())**2)
        
        # 初始化健康度（全部为1.0，即完全健康）
        self.initial_health = np.ones(len(self.node_coords))
        
    def calculate_health_at_time(self, t, total_time=50):
        """计算时间t时的健康度分布"""
        health = self.initial_health.copy()
        
        # 时间进展比例
        time_progress = t / total_time
        
        for source in self.pollution_sources:
            center_x, center_y = source['center']
            intensity = source['intensity']
            spread_rate = source['spread_rate']
            
            # 计算距离
            distances = np.sqrt((self.x - center_x)**2 + (self.y - center_y)**2)
            normalized_distances = distances / self.domain_size
            
            # 污染扩散模型：随时间增长的高斯扩散
            spread_radius = spread_rate * time_progress
            pollution_effect = intensity * np.exp(-normalized_distances**2 / (2 * spread_radius**2))
            
            # 时间衰减因子：污染随时间加重
            time_factor = 1 - np.exp(-3 * time_progress)
            
            # 应用污染效应
            health -= pollution_effect * time_factor
        
        # 添加随机噪声模拟自然变化
        noise = np.random.normal(0, 0.02, len(health))
        health += noise
        
        # 确保健康度在[0, 1]范围内
        health = np.clip(health, 0.0, 1.0)
        
        return health
    
    def create_static_progression(self, save_dir, time_points=[0, 15, 30, 45]):
        """创建静态的健康度退化进程图"""
        os.makedirs(save_dir, exist_ok=True)
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('珍珠养殖水域健康度退化进程', fontsize=16, fontweight='bold')
        
        for i, t in enumerate(time_points):
            ax = axes[i//2, i%2]
            
            health = self.calculate_health_at_time(t)
            
            # 绘制等高线图
            tcf = ax.tricontourf(self.triang, health, levels=50, cmap=self.health_cmap, vmin=0, vmax=1)
            
            # 标记污染源
            for j, source in enumerate(self.pollution_sources):
                center_x, center_y = source['center']
                ax.plot(center_x, center_y, 'k*', markersize=15, markeredgecolor='white', 
                       markeredgewidth=2, label=f'污染源{j+1}' if i == 0 else "")
            
            ax.set_xlabel('X坐标 (m)')
            ax.set_ylabel('Y坐标 (m)')
            ax.set_title(f'时间: {t}天\n平均健康度: {np.mean(health):.3f}')
            ax.set_aspect('equal')
            
            # 添加颜色条
            cbar = plt.colorbar(tcf, ax=ax)
            cbar.set_label('健康度指数')
            
            # 添加统计信息
            unhealthy_ratio = np.sum(health < 0.5) / len(health) * 100
            critical_ratio = np.sum(health < 0.3) / len(health) * 100
            
            stats_text = f'不健康区域: {unhealthy_ratio:.1f}%\n危险区域: {critical_ratio:.1f}%'
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                   verticalalignment='top', fontsize=10,
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
            
            if i == 0:
                ax.legend(loc='upper right')
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'health_degradation_progression.png'), dpi=300, bbox_inches='tight')
        print(f"✅ 健康度退化进程图已保存: {os.path.join(save_dir, 'health_degradation_progression.png')}")
        plt.close()
    
    def create_frame_sequence(self, save_dir, total_frames=50):
        """创建健康度退化帧序列"""
        os.makedirs(save_dir, exist_ok=True)
        frames_dir = os.path.join(save_dir, 'frames')
        os.makedirs(frames_dir, exist_ok=True)

        # 预计算所有时间点的统计数据
        time_data = []
        mean_health_data = []
        unhealthy_ratio_data = []
        critical_ratio_data = []

        print("正在生成帧序列...")
        for frame in range(total_frames):
            if frame % 10 == 0:
                print(f"  处理第 {frame}/{total_frames} 帧...")

            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
            fig.suptitle(f'珍珠养殖水域健康度动态退化过程 - 第{frame}天', fontsize=16, fontweight='bold')

            # 计算当前时间的健康度
            health = self.calculate_health_at_time(frame, total_frames)

            # 左图：健康度分布
            tcf1 = ax1.tricontourf(self.triang, health, levels=50, cmap=self.health_cmap, vmin=0, vmax=1)

            # 标记污染源
            for j, source in enumerate(self.pollution_sources):
                center_x, center_y = source['center']
                ax1.plot(center_x, center_y, 'k*', markersize=15, markeredgecolor='white',
                        markeredgewidth=2, label=f'污染源{j+1}')

            ax1.set_xlabel('X坐标 (m)')
            ax1.set_ylabel('Y坐标 (m)')
            ax1.set_title('健康度空间分布')
            ax1.set_aspect('equal')
            if frame == 0:
                ax1.legend()

            # 添加颜色条
            cbar1 = plt.colorbar(tcf1, ax=ax1)
            cbar1.set_label('健康度指数')

            # 计算统计数据
            mean_health = np.mean(health) * 100
            unhealthy_ratio = np.sum(health < 0.5) / len(health) * 100
            critical_ratio = np.sum(health < 0.3) / len(health) * 100

            time_data.append(frame)
            mean_health_data.append(mean_health)
            unhealthy_ratio_data.append(unhealthy_ratio)
            critical_ratio_data.append(critical_ratio)

            # 右图：统计趋势
            ax2.plot(time_data, mean_health_data, 'g-', linewidth=3, label='平均健康度 (%)')
            ax2.plot(time_data, unhealthy_ratio_data, 'orange', linewidth=3, label='不健康区域 (%)')
            ax2.plot(time_data, critical_ratio_data, 'r-', linewidth=3, label='危险区域 (%)')

            ax2.set_xlabel('时间 (天)')
            ax2.set_ylabel('比例 (%)')
            ax2.set_title('健康度统计趋势')
            ax2.grid(True, alpha=0.3)
            ax2.set_xlim(0, total_frames)
            ax2.set_ylim(0, 100)
            if frame == 0:
                ax2.legend()

            # 添加当前状态文本
            status_text = f'第 {frame} 天\n平均健康度: {mean_health:.1f}%\n不健康区域: {unhealthy_ratio:.1f}%\n危险区域: {critical_ratio:.1f}%'

            # 根据健康度设置文本颜色
            if mean_health > 70:
                text_color = 'green'
            elif mean_health > 40:
                text_color = 'orange'
            else:
                text_color = 'red'

            ax1.text(0.02, 0.98, status_text, transform=ax1.transAxes,
                    verticalalignment='top', fontsize=12, fontweight='bold', color=text_color,
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.9))

            plt.tight_layout()

            # 保存帧
            frame_path = os.path.join(frames_dir, f'frame_{frame:03d}.png')
            plt.savefig(frame_path, dpi=150, bbox_inches='tight')
            plt.close()

        print(f"✅ 健康度退化帧序列已保存: {frames_dir}")
        return time_data, mean_health_data, unhealthy_ratio_data, critical_ratio_data
    
    def create_target_points_analysis(self, save_dir):
        """创建靶向复核目标点分析"""
        os.makedirs(save_dir, exist_ok=True)
        
        # 选择一个中等退化时间点
        t_analysis = 30
        health = self.calculate_health_at_time(t_analysis)
        
        # 识别需要复核的目标点（健康度低于阈值）
        threshold = 0.4
        target_indices = np.where(health < threshold)[0]
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
        fig.suptitle(f'靶向复核模式：目标点选择与路径规划 (第{t_analysis}天)', fontsize=16, fontweight='bold')
        
        # 左图：目标点识别
        tcf1 = ax1.tricontourf(self.triang, health, levels=50, cmap=self.health_cmap, vmin=0, vmax=1)
        
        # 标记目标点
        target_x = self.x[target_indices]
        target_y = self.y[target_indices]
        ax1.scatter(target_x, target_y, c='blue', s=30, alpha=0.8, label=f'目标点 ({len(target_indices)}个)')
        
        # 标记污染源
        for j, source in enumerate(self.pollution_sources):
            center_x, center_y = source['center']
            ax1.plot(center_x, center_y, 'k*', markersize=15, markeredgecolor='white', 
                    markeredgewidth=2, label=f'污染源{j+1}')
        
        # 标记起点和终点
        start_point = (self.x.min() + 0.1 * (self.x.max() - self.x.min()), 
                      self.y.min() + 0.1 * (self.y.max() - self.y.min()))
        end_point = (self.x.max() - 0.1 * (self.x.max() - self.x.min()), 
                    self.y.max() - 0.1 * (self.y.max() - self.y.min()))
        
        ax1.plot(start_point[0], start_point[1], 'gs', markersize=12, label='起点(s)')
        ax1.plot(end_point[0], end_point[1], 'rs', markersize=12, label='终点(e)')
        
        ax1.set_xlabel('X坐标 (m)')
        ax1.set_ylabel('Y坐标 (m)')
        ax1.set_title('目标点识别与分布')
        ax1.set_aspect('equal')
        ax1.legend()
        
        # 添加颜色条
        cbar1 = plt.colorbar(tcf1, ax=ax1)
        cbar1.set_label('健康度指数')
        
        # 右图：健康度分布直方图
        ax2.hist(health, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        ax2.axvline(x=threshold, color='red', linestyle='--', linewidth=2, 
                   label=f'复核阈值 ({threshold})')
        ax2.axvline(x=np.mean(health), color='green', linestyle='-', linewidth=2, 
                   label=f'平均值 ({np.mean(health):.3f})')
        
        ax2.set_xlabel('健康度指数')
        ax2.set_ylabel('节点数量')
        ax2.set_title('健康度分布统计')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # 添加统计信息
        total_nodes = len(health)
        target_ratio = len(target_indices) / total_nodes * 100
        
        stats_text = (f'总节点数: {total_nodes:,}\n'
                     f'目标点数: {len(target_indices):,}\n'
                     f'目标比例: {target_ratio:.1f}%\n'
                     f'平均健康度: {np.mean(health):.3f}\n'
                     f'最低健康度: {np.min(health):.3f}')
        
        ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, 
                verticalalignment='top', fontsize=11,
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'target_points_analysis.png'), dpi=300, bbox_inches='tight')
        print(f"✅ 目标点分析图已保存: {os.path.join(save_dir, 'target_points_analysis.png')}")
        plt.close()
        
        return target_indices, health

    def create_comprehensive_trend_analysis(self, save_dir, time_data, mean_health_data, unhealthy_ratio_data, critical_ratio_data):
        """创建综合趋势分析"""
        os.makedirs(save_dir, exist_ok=True)

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('珍珠养殖水域健康度退化综合分析', fontsize=16, fontweight='bold')

        # 1. 健康度趋势
        ax1.plot(time_data, mean_health_data, 'g-', linewidth=3, label='平均健康度')
        ax1.fill_between(time_data, mean_health_data, alpha=0.3, color='green')
        ax1.axhline(y=70, color='orange', linestyle='--', label='警告线 (70%)')
        ax1.axhline(y=50, color='red', linestyle='--', label='危险线 (50%)')

        ax1.set_xlabel('时间 (天)')
        ax1.set_ylabel('平均健康度 (%)')
        ax1.set_title('健康度时间演化')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        ax1.set_ylim(0, 100)

        # 2. 问题区域比例
        ax2.plot(time_data, unhealthy_ratio_data, 'orange', linewidth=3, label='不健康区域 (<50%)')
        ax2.plot(time_data, critical_ratio_data, 'r-', linewidth=3, label='危险区域 (<30%)')
        ax2.fill_between(time_data, unhealthy_ratio_data, alpha=0.3, color='orange')
        ax2.fill_between(time_data, critical_ratio_data, alpha=0.3, color='red')

        ax2.set_xlabel('时间 (天)')
        ax2.set_ylabel('问题区域比例 (%)')
        ax2.set_title('问题区域扩散趋势')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        ax2.set_ylim(0, 100)

        # 3. 退化速率分析
        health_gradient = np.gradient(mean_health_data)
        ax3.plot(time_data[1:], health_gradient[1:], 'b-', linewidth=2, marker='o', markersize=4)
        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax3.fill_between(time_data[1:], health_gradient[1:], 0, alpha=0.3, color='blue')

        ax3.set_xlabel('时间 (天)')
        ax3.set_ylabel('健康度变化率 (%/天)')
        ax3.set_title('健康度退化速率')
        ax3.grid(True, alpha=0.3)

        # 标记最大退化速率点
        max_decline_idx = np.argmin(health_gradient[1:]) + 1
        ax3.plot(time_data[max_decline_idx], health_gradient[max_decline_idx], 'ro', markersize=10,
                label=f'最大退化点 (第{time_data[max_decline_idx]}天)')
        ax3.legend()

        # 4. 阶段性分析
        stages = ['健康期', '警告期', '危险期', '严重期']
        stage_colors = ['green', 'yellow', 'orange', 'red']
        stage_thresholds = [70, 50, 30, 0]

        # 计算各阶段持续时间
        stage_durations = []
        current_stage = 0

        for i, health in enumerate(mean_health_data):
            if health <= stage_thresholds[current_stage + 1] and current_stage < 3:
                current_stage += 1

        # 绘制阶段分布饼图
        stage_data = []
        stage_labels = []

        for i in range(len(stages)):
            if i < current_stage + 1:
                if i == current_stage:
                    duration = len(time_data) - sum(stage_data)
                else:
                    # 估算阶段持续时间
                    threshold_day = next((day for day, h in zip(time_data, mean_health_data)
                                        if h <= stage_thresholds[i+1]), len(time_data))
                    prev_threshold_day = 0 if i == 0 else next((day for day, h in zip(time_data, mean_health_data)
                                                              if h <= stage_thresholds[i]), 0)
                    duration = threshold_day - prev_threshold_day

                stage_data.append(duration)
                stage_labels.append(f'{stages[i]}\n({duration}天)')

        if stage_data:
            wedges, texts, autotexts = ax4.pie(stage_data, labels=stage_labels, colors=stage_colors[:len(stage_data)],
                                              autopct='%1.1f%%', startangle=90)
            ax4.set_title('健康度阶段分布')

        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'comprehensive_trend_analysis.png'), dpi=300, bbox_inches='tight')
        print(f"✅ 综合趋势分析图已保存: {os.path.join(save_dir, 'comprehensive_trend_analysis.png')}")
        plt.close()

def main():
    """主函数"""
    print("=== 健康度动态退化可视化 ===")
    
    # 加载网格数据
    mesh_path = 'data/mesh/lake_box_mesh_detailed.msh'
    node_coords, elements = load_msh_data(mesh_path)
    if node_coords is None:
        print("❌ 无法加载网格文件")
        return
    
    print(f"✅ 成功加载网格: {len(node_coords)} 个节点")
    
    # 初始化动画器
    animator = HealthDegradationAnimator(node_coords, elements)
    save_dir = 'results/health_degradation_animation'
    
    # 1. 创建静态进程图
    print("1. 生成健康度退化进程图...")
    animator.create_static_progression(save_dir)
    
    # 2. 创建帧序列
    print("2. 生成健康度退化帧序列...")
    time_data, mean_health_data, unhealthy_ratio_data, critical_ratio_data = animator.create_frame_sequence(save_dir, total_frames=50)
    
    # 3. 创建综合趋势分析
    print("3. 生成综合趋势分析...")
    animator.create_comprehensive_trend_analysis(save_dir, time_data, mean_health_data, unhealthy_ratio_data, critical_ratio_data)

    # 4. 创建目标点分析
    print("4. 生成靶向复核目标点分析...")
    target_indices, health = animator.create_target_points_analysis(save_dir)

    print(f"\n✅ 所有可视化完成！结果保存在: {save_dir}")
    print(f"📊 识别到 {len(target_indices)} 个需要复核的目标点")
    print(f"🎬 生成了 50 帧动态图像，展示健康度从绿色到红色的退化过程")

if __name__ == '__main__':
    main()
