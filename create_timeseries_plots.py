#!/usr/bin/env python3
"""
时序图可视化脚本
专门用于绘制温度、pH、电导率、叶绿素的时序图
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import sys

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_master_dataset(data_path):
    """加载主数据集"""
    try:
        data = np.load(data_path)
        timeseries = data['timeseries']  # Shape: (N_nodes, T, N_features)
        node_coords = data['node_coords']
        print(f"✅ 成功加载数据集，形状: {timeseries.shape}")
        print(f"   节点数: {timeseries.shape[0]}")
        print(f"   时间步数: {timeseries.shape[1]}")
        print(f"   特征数: {timeseries.shape[2]}")
        return timeseries, node_coords
    except Exception as e:
        print(f"❌ 加载数据集失败: {e}")
        return None, None

def plot_enhanced_timeseries(timeseries_data, save_dir):
    """
    绘制增强版时序图
    
    Args:
        timeseries_data: 形状为 (N_nodes, T, N_features) 的数组
        save_dir: 保存目录
    """
    # 特征名称和单位
    feature_info = [
        ('Temperature', '°C', 'red'),
        ('pH', '', 'blue'), 
        ('Conductivity', 'μS/cm', 'green'),
        ('Chlorophyll', 'μg/L', 'orange')
    ]
    
    # 创建保存目录
    os.makedirs(save_dir, exist_ok=True)
    
    # 1. 绘制所有参数的综合时序图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Water Quality Parameters Time Series Analysis', fontsize=18, fontweight='bold')
    
    for i, (name, unit, color) in enumerate(feature_info):
        ax = axes[i//2, i%2]
        
        # 计算统计量
        data = timeseries_data[:, :, i]  # 选择第i个特征
        mean_vals = np.mean(data, axis=0)
        min_vals = np.min(data, axis=0)
        max_vals = np.max(data, axis=0)
        std_vals = np.std(data, axis=0)
        
        time_steps = np.arange(len(mean_vals))
        
        # 绘制均值线
        ax.plot(time_steps, mean_vals, color=color, linewidth=3, label='Mean', alpha=0.9)
        
        # 绘制标准差范围
        ax.fill_between(time_steps, mean_vals - std_vals, mean_vals + std_vals, 
                       color=color, alpha=0.3, label='±1 Std Dev')
        
        # 绘制最大最小值范围
        ax.fill_between(time_steps, min_vals, max_vals, 
                       color=color, alpha=0.1, label='Min-Max Range')
        
        # 设置标题和标签
        title = f'{name} ({unit})' if unit else name
        ax.set_title(title, fontsize=14, fontweight='bold')
        ax.set_xlabel('Time Step', fontsize=12)
        ax.set_ylabel(title, fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend(loc='upper right', fontsize=10)
        
        # 添加统计信息文本框
        stats_text = f'Overall Mean: {np.mean(data):.2f}\nOverall Std: {np.std(data):.2f}\nRange: [{np.min(data):.2f}, {np.max(data):.2f}]'
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
               verticalalignment='top', fontsize=9,
               bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'comprehensive_timeseries.png'), dpi=300, bbox_inches='tight')
    print(f"✅ 综合时序图已保存: {os.path.join(save_dir, 'comprehensive_timeseries.png')}")
    plt.close()
    
    # 2. 为每个参数单独绘制详细的时序图
    for i, (name, unit, color) in enumerate(feature_info):
        fig, ax = plt.subplots(figsize=(14, 8))
        
        data = timeseries_data[:, :, i]
        mean_vals = np.mean(data, axis=0)
        min_vals = np.min(data, axis=0)
        max_vals = np.max(data, axis=0)
        std_vals = np.std(data, axis=0)
        
        time_steps = np.arange(len(mean_vals))
        
        # 绘制多条线
        ax.plot(time_steps, mean_vals, color=color, linewidth=3, label='Mean Value', alpha=0.9)
        ax.plot(time_steps, min_vals, color='darkred', linewidth=2, linestyle='--', label='Minimum Value', alpha=0.7)
        ax.plot(time_steps, max_vals, color='darkgreen', linewidth=2, linestyle='--', label='Maximum Value', alpha=0.7)
        
        # 填充区域
        ax.fill_between(time_steps, mean_vals - std_vals, mean_vals + std_vals, 
                       color=color, alpha=0.3, label='±1 Standard Deviation')
        ax.fill_between(time_steps, min_vals, max_vals, 
                       color=color, alpha=0.1, label='Full Range')
        
        # 设置标题和标签
        title = f'{name} Time Series Analysis'
        unit_label = f' ({unit})' if unit else ''
        ax.set_title(title, fontsize=16, fontweight='bold')
        ax.set_xlabel('Time Step', fontsize=14)
        ax.set_ylabel(f'{name}{unit_label}', fontsize=14)
        ax.grid(True, alpha=0.3)
        ax.legend(loc='upper right', fontsize=12)
        
        # 添加详细统计信息
        stats_text = (f'Statistics:\n'
                     f'Mean: {np.mean(data):.3f}\n'
                     f'Std Dev: {np.std(data):.3f}\n'
                     f'Min: {np.min(data):.3f}\n'
                     f'Max: {np.max(data):.3f}\n'
                     f'Range: {np.max(data) - np.min(data):.3f}')
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
               verticalalignment='top', fontsize=11,
               bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.9))
        
        plt.tight_layout()
        filename = f'{name.lower()}_detailed_timeseries.png'
        plt.savefig(os.path.join(save_dir, filename), dpi=300, bbox_inches='tight')
        print(f"✅ {name} 详细时序图已保存: {os.path.join(save_dir, filename)}")
        plt.show()

def plot_correlation_analysis(timeseries_data, save_dir):
    """绘制参数间相关性分析"""
    feature_names = ['Temperature', 'pH', 'Conductivity', 'Chlorophyll']
    
    # 计算每个时间步的平均值
    mean_data = np.mean(timeseries_data, axis=0)  # Shape: (T, N_features)
    
    # 计算相关性矩阵
    correlation_matrix = np.corrcoef(mean_data.T)
    
    # 绘制相关性热力图
    fig, ax = plt.subplots(figsize=(10, 8))
    im = ax.imshow(correlation_matrix, cmap='coolwarm', vmin=-1, vmax=1)
    
    # 设置标签
    ax.set_xticks(range(len(feature_names)))
    ax.set_yticks(range(len(feature_names)))
    ax.set_xticklabels(feature_names, fontsize=12)
    ax.set_yticklabels(feature_names, fontsize=12)
    
    # 添加数值标注
    for i in range(len(feature_names)):
        for j in range(len(feature_names)):
            text = ax.text(j, i, f'{correlation_matrix[i, j]:.3f}',
                         ha="center", va="center", color="black", 
                         fontweight='bold', fontsize=12)
    
    ax.set_title('Water Quality Parameters Correlation Matrix', fontsize=16, fontweight='bold')
    cbar = plt.colorbar(im, ax=ax)
    cbar.set_label('Correlation Coefficient', fontsize=12)
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'correlation_matrix.png'), dpi=300, bbox_inches='tight')
    print(f"✅ 相关性矩阵已保存: {os.path.join(save_dir, 'correlation_matrix.png')}")
    plt.show()

def main():
    """主函数"""
    print("=== 水质参数时序图可视化 ===")
    
    # 数据路径
    data_path = 'data/master/master_timeseries_dataset.npz'
    save_dir = 'results/enhanced_timeseries_plots'
    
    # 加载数据
    timeseries_data, node_coords = load_master_dataset(data_path)
    if timeseries_data is None:
        print("❌ 无法加载数据，程序退出")
        return
    
    # 绘制时序图
    print("\n--- 开始绘制时序图 ---")
    plot_enhanced_timeseries(timeseries_data, save_dir)
    
    # 绘制相关性分析
    print("\n--- 开始绘制相关性分析 ---")
    plot_correlation_analysis(timeseries_data, save_dir)
    
    print(f"\n✅ 所有图表已保存至目录: {save_dir}")

if __name__ == '__main__':
    main()
