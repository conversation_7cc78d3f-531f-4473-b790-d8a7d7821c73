#!/usr/bin/env python3
"""
应急数据集可视化工具 - 支持选择性可视化
用户可以选择要可视化的参数和时间范围
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import argparse
from scipy.interpolate import griddata

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class EmergencyDataVisualizer:
    """应急数据可视化器"""
    
    def __init__(self):
        self.feature_info = {
            0: ('Temperature', '°C', 'red'),
            1: ('pH', '', 'blue'),
            2: ('Conductivity', 'μS/cm', 'green'),
            3: ('Chlorophyll', 'μg/L', 'orange'),
            4: ('Health Index', '', 'purple')
        }
        
        self.feature_names = ['temperature', 'ph', 'conductivity', 'chlorophyll', 'health_index']
    
    def load_emergency_data(self, data_path):
        """加载应急数据集"""
        try:
            data = np.load(data_path)
            timeseries = data['timeseries']
            node_coords = data['node_coords']
            description = data.get('description', 'Emergency dataset')
            print(f"✅ 成功加载应急数据集")
            print(f"   描述: {description}")
            print(f"   数据形状: {timeseries.shape}")
            return timeseries, node_coords
        except Exception as e:
            print(f"❌ 加载数据集失败: {e}")
            return None, None
    
    def plot_selected_timeseries(self, timeseries_data, selected_features, time_range, save_dir):
        """
        绘制选定特征的时序图
        
        Args:
            timeseries_data: 时间序列数据
            selected_features: 选定的特征索引列表
            time_range: 时间范围 [start, end]
            save_dir: 保存目录
        """
        os.makedirs(save_dir, exist_ok=True)
        
        # 提取时间范围
        start_time, end_time = time_range
        data_subset = timeseries_data[:, start_time:end_time+1, :]
        time_steps = np.arange(start_time, end_time+1)
        
        # 计算子图布局
        n_features = len(selected_features)
        if n_features <= 2:
            rows, cols = 1, n_features
            figsize = (8*cols, 6)
        elif n_features <= 4:
            rows, cols = 2, 2
            figsize = (16, 12)
        else:
            rows, cols = 3, 2
            figsize = (16, 18)
        
        fig, axes = plt.subplots(rows, cols, figsize=figsize)
        if n_features == 1:
            axes = [axes]
        elif rows == 1:
            axes = axes if n_features > 1 else [axes]
        else:
            axes = axes.flatten()
        
        fig.suptitle(f'Emergency Dataset - Selected Parameters (Time Steps {start_time}-{end_time})', 
                    fontsize=16, fontweight='bold')
        
        for i, feature_idx in enumerate(selected_features):
            if i >= len(axes):
                break
                
            ax = axes[i]
            name, unit, color = self.feature_info[feature_idx]
            
            # 计算统计量
            data = data_subset[:, :, feature_idx]
            mean_vals = np.mean(data, axis=0)
            min_vals = np.min(data, axis=0)
            max_vals = np.max(data, axis=0)
            std_vals = np.std(data, axis=0)
            
            # 绘制时序图
            ax.plot(time_steps, mean_vals, color=color, linewidth=3, label='Mean', alpha=0.9)
            ax.fill_between(time_steps, mean_vals - std_vals, mean_vals + std_vals, 
                           color=color, alpha=0.3, label='±1 Std Dev')
            ax.fill_between(time_steps, min_vals, max_vals, 
                           color=color, alpha=0.1, label='Min-Max Range')
            
            # 设置标题和标签
            title = f'{name} ({unit})' if unit else name
            ax.set_title(title, fontsize=14, fontweight='bold')
            ax.set_xlabel('Time Step', fontsize=12)
            ax.set_ylabel(title, fontsize=12)
            ax.grid(True, alpha=0.3)
            ax.legend(loc='upper right', fontsize=10)
            
            # 添加统计信息
            stats_text = (f'Mean: {np.mean(data):.2f}\n'
                         f'Range: [{np.min(data):.2f}, {np.max(data):.2f}]')
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                   verticalalignment='top', fontsize=10,
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
        
        # 隐藏多余的子图
        for i in range(len(selected_features), len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        filename = f'selected_timeseries_t{start_time}-{end_time}.png'
        plt.savefig(os.path.join(save_dir, filename), dpi=300, bbox_inches='tight')
        print(f"✅ 选定时序图已保存: {os.path.join(save_dir, filename)}")
        plt.close()
    
    def plot_spatial_snapshots(self, timeseries_data, node_coords, feature_idx, time_points, save_dir):
        """
        绘制指定时间点的空间分布快照
        
        Args:
            timeseries_data: 时间序列数据
            node_coords: 节点坐标
            feature_idx: 特征索引
            time_points: 时间点列表
            save_dir: 保存目录
        """
        os.makedirs(save_dir, exist_ok=True)
        
        name, unit, color = self.feature_info[feature_idx]
        x, y = node_coords[:, 0], node_coords[:, 1]
        
        # 创建网格用于插值
        grid_x, grid_y = np.mgrid[min(x):max(x):200j, min(y):max(y):200j]
        
        n_snapshots = len(time_points)
        cols = min(4, n_snapshots)
        rows = (n_snapshots + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(5*cols, 4*rows))
        if n_snapshots == 1:
            axes = [axes]
        elif rows == 1:
            axes = axes if n_snapshots > 1 else [axes]
        else:
            axes = axes.flatten()
        
        fig.suptitle(f'{name} Spatial Distribution - Emergency Dataset', fontsize=16, fontweight='bold')
        
        # 计算全局颜色范围
        all_values = timeseries_data[:, time_points, feature_idx]
        vmin, vmax = np.min(all_values), np.max(all_values)
        
        for i, t in enumerate(time_points):
            if i >= len(axes):
                break
                
            ax = axes[i]
            values = timeseries_data[:, t, feature_idx]
            
            # 插值到网格
            grid_z = griddata((x, y), values, (grid_x, grid_y), method='cubic')
            
            # 绘制等高线图
            im = ax.imshow(grid_z.T, extent=(min(x), max(x), min(y), max(y)), 
                          origin='lower', cmap='viridis', vmin=vmin, vmax=vmax)
            
            # 添加散点
            scatter = ax.scatter(x, y, c=values, cmap='viridis', s=10, 
                               edgecolors='black', alpha=0.7, vmin=vmin, vmax=vmax)
            
            ax.set_title(f'Time Step {t}', fontsize=12, fontweight='bold')
            ax.set_xlabel('X Coordinate')
            ax.set_ylabel('Y Coordinate')
            ax.set_aspect('equal')
            
            # 添加数值信息
            stats_text = f'Mean: {np.mean(values):.2f}\nMax: {np.max(values):.2f}'
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                   verticalalignment='top', fontsize=9,
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        # 隐藏多余的子图
        for i in range(len(time_points), len(axes)):
            axes[i].set_visible(False)
        
        # 添加颜色条
        if len(time_points) > 0:
            cbar_ax = fig.add_axes([0.92, 0.15, 0.02, 0.7])
            unit_label = f' ({unit})' if unit else ''
            fig.colorbar(im, cax=cbar_ax, label=f'{name}{unit_label}')
        
        plt.tight_layout()
        plt.subplots_adjust(right=0.9)
        
        filename = f'{self.feature_names[feature_idx]}_spatial_snapshots.png'
        plt.savefig(os.path.join(save_dir, filename), dpi=300, bbox_inches='tight')
        print(f"✅ 空间分布快照已保存: {os.path.join(save_dir, filename)}")
        plt.close()
    
    def plot_chlorophyll_health_correlation(self, timeseries_data, save_dir):
        """绘制叶绿素与健康度的相关性分析"""
        os.makedirs(save_dir, exist_ok=True)
        
        # 提取叶绿素和健康度数据
        chlorophyll_data = timeseries_data[:, :, 3].flatten()  # 叶绿素
        health_data = timeseries_data[:, :, 4].flatten()       # 健康度
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        
        # 散点图
        ax1.scatter(chlorophyll_data, health_data, alpha=0.5, s=1)
        ax1.set_xlabel('Chlorophyll (μg/L)', fontsize=12)
        ax1.set_ylabel('Health Index', fontsize=12)
        ax1.set_title('Chlorophyll vs Health Index Correlation', fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3)
        
        # 计算相关系数
        correlation = np.corrcoef(chlorophyll_data, health_data)[0, 1]
        ax1.text(0.05, 0.95, f'Correlation: {correlation:.3f}', 
                transform=ax1.transAxes, fontsize=12,
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        # 时间序列对比
        time_steps = np.arange(timeseries_data.shape[1])
        chlorophyll_mean = np.mean(timeseries_data[:, :, 3], axis=0)
        health_mean = np.mean(timeseries_data[:, :, 4], axis=0)
        
        ax2_twin = ax2.twinx()
        
        line1 = ax2.plot(time_steps, chlorophyll_mean, 'orange', linewidth=3, label='Chlorophyll')
        line2 = ax2_twin.plot(time_steps, health_mean, 'purple', linewidth=3, label='Health Index')
        
        ax2.set_xlabel('Time Step', fontsize=12)
        ax2.set_ylabel('Chlorophyll (μg/L)', color='orange', fontsize=12)
        ax2_twin.set_ylabel('Health Index', color='purple', fontsize=12)
        ax2.set_title('Chlorophyll Outbreak vs Health Decline', fontsize=14, fontweight='bold')
        
        # 合并图例
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax2.legend(lines, labels, loc='center right')
        
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'chlorophyll_health_correlation.png'), dpi=300, bbox_inches='tight')
        print(f"✅ 叶绿素-健康度相关性分析已保存: {os.path.join(save_dir, 'chlorophyll_health_correlation.png')}")
        plt.close()

def main():
    """主函数 - 支持命令行参数"""
    parser = argparse.ArgumentParser(description='Emergency Dataset Visualization Tool')
    parser.add_argument('--data', default='data/emergency/chlorophyll_outbreak_dataset.npz',
                       help='Path to emergency dataset')
    parser.add_argument('--features', nargs='+', type=int, default=[0,1,2,3,4],
                       help='Feature indices to visualize (0:temp, 1:pH, 2:conductivity, 3:chlorophyll, 4:health)')
    parser.add_argument('--time-range', nargs=2, type=int, default=[0, 29],
                       help='Time range to visualize [start, end]')
    parser.add_argument('--snapshots', nargs='+', type=int, default=[0, 10, 20, 29],
                       help='Time points for spatial snapshots')
    parser.add_argument('--output', default='results/emergency_visualization',
                       help='Output directory')
    
    args = parser.parse_args()
    
    print("=== 应急数据集可视化工具 ===")
    print(f"数据路径: {args.data}")
    print(f"选定特征: {args.features}")
    print(f"时间范围: {args.time_range}")
    print(f"快照时间点: {args.snapshots}")
    
    # 初始化可视化器
    visualizer = EmergencyDataVisualizer()
    
    # 加载数据
    timeseries_data, node_coords = visualizer.load_emergency_data(args.data)
    if timeseries_data is None:
        return
    
    # 验证参数
    max_time = timeseries_data.shape[1] - 1
    args.time_range = [max(0, args.time_range[0]), min(max_time, args.time_range[1])]
    args.snapshots = [t for t in args.snapshots if 0 <= t <= max_time]
    args.features = [f for f in args.features if 0 <= f <= 4]
    
    print(f"\n调整后参数:")
    print(f"时间范围: {args.time_range}")
    print(f"快照时间点: {args.snapshots}")
    print(f"有效特征: {args.features}")
    
    # 创建可视化
    print(f"\n--- 开始生成可视化 ---")
    
    # 1. 选定特征的时序图
    if args.features:
        visualizer.plot_selected_timeseries(timeseries_data, args.features, args.time_range, args.output)
    
    # 2. 叶绿素空间分布快照
    if 3 in args.features and args.snapshots:
        visualizer.plot_spatial_snapshots(timeseries_data, node_coords, 3, args.snapshots, args.output)
    
    # 3. 健康度空间分布快照
    if 4 in args.features and args.snapshots:
        visualizer.plot_spatial_snapshots(timeseries_data, node_coords, 4, args.snapshots, args.output)
    
    # 4. 叶绿素-健康度相关性分析
    if 3 in args.features and 4 in args.features:
        visualizer.plot_chlorophyll_health_correlation(timeseries_data, args.output)
    
    print(f"\n✅ 所有可视化已完成，保存至: {args.output}")

if __name__ == '__main__':
    main()
