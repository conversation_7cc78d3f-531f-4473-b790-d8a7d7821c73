#!/usr/bin/env python3
"""
应急数据集生成器 - 叶绿素爆发前期模拟
模拟叶绿素浓度逐渐升高，导致健康度降低的过程
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import sys
from scipy.ndimage import gaussian_filter
from scipy.interpolate import griddata

# 添加项目根目录到Python路径
sys.path.append(os.getcwd())

from shared_utils.mesh_loader import load_msh_data
from dynamic_mathematical_model.model_core import PearlMusselModel

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class EmergencyDataGenerator:
    """应急数据生成器 - 专门用于模拟叶绿素爆发情况"""
    
    def __init__(self, seed=123):
        self.seed = seed
        np.random.seed(seed)
        
        # 叶绿素爆发的参数设置
        self.chlorophyll_params = {
            'initial_mean': 8.0,      # 初始平均浓度 (μg/L)
            'peak_mean': 85.0,        # 爆发峰值 (μg/L)
            'growth_rate': 0.15,      # 增长率
            'hotspot_intensity': 2.5, # 热点强度倍数
            'spatial_correlation': 6.0 # 空间相关长度
        }
        
        # 其他参数的正常范围
        self.normal_params = {
            'temperature': {'mean': 24.0, 'std': 2.0, 'min': 20.0, 'max': 28.0},
            'ph': {'mean': 7.8, 'std': 0.5, 'min': 7.0, 'max': 8.5},
            'conductivity': {'mean': 450.0, 'std': 80.0, 'min': 300.0, 'max': 600.0}
        }
    
    def generate_chlorophyll_outbreak(self, coords, timestep, total_timesteps):
        """
        生成叶绿素爆发数据
        
        Args:
            coords: 节点坐标
            timestep: 当前时间步
            total_timesteps: 总时间步数
        """
        # 计算时间进展比例 (0 到 1)
        time_progress = timestep / (total_timesteps - 1)
        
        # 使用S型曲线模拟叶绿素爆发过程
        # 前期缓慢增长，中期快速爆发，后期趋于稳定
        sigmoid_factor = 1 / (1 + np.exp(-10 * (time_progress - 0.6)))
        current_mean = (self.chlorophyll_params['initial_mean'] + 
                       (self.chlorophyll_params['peak_mean'] - self.chlorophyll_params['initial_mean']) * sigmoid_factor)
        
        # 创建空间热点 - 模拟营养物质聚集区域
        x, y = coords[:, 0], coords[:, 1]
        
        # 定义几个叶绿素爆发热点
        hotspots = [
            (np.mean(x) + 0.3 * (np.max(x) - np.min(x)), np.mean(y) + 0.2 * (np.max(y) - np.min(y))),
            (np.mean(x) - 0.2 * (np.max(x) - np.min(x)), np.mean(y) - 0.3 * (np.max(y) - np.min(y))),
            (np.mean(x) + 0.1 * (np.max(x) - np.min(x)), np.mean(y) - 0.1 * (np.max(y) - np.min(y)))
        ]
        
        # 基础浓度场
        base_field = np.full(len(coords), current_mean)
        
        # 添加热点效应
        for hx, hy in hotspots:
            distances = np.sqrt((x - hx)**2 + (y - hy)**2)
            max_distance = np.sqrt((np.max(x) - np.min(x))**2 + (np.max(y) - np.min(y))**2)
            normalized_distances = distances / max_distance
            
            # 热点强度随时间和距离变化
            hotspot_effect = (self.chlorophyll_params['hotspot_intensity'] * current_mean * 
                            np.exp(-5 * normalized_distances) * sigmoid_factor)
            base_field += hotspot_effect
        
        # 添加空间平滑和随机变化
        grid_size = 50
        xi = np.linspace(x.min(), x.max(), grid_size)
        yi = np.linspace(y.min(), y.max(), grid_size)
        xi_grid, yi_grid = np.meshgrid(xi, yi)
        
        # 生成随机噪声场
        noise = np.random.normal(0, current_mean * 0.2, (grid_size, grid_size))
        sigma = self.chlorophyll_params['spatial_correlation'] * grid_size / (x.max() - x.min())
        smooth_noise = gaussian_filter(noise, sigma=sigma)
        
        # 插值到实际节点位置
        points = np.column_stack((xi_grid.ravel(), yi_grid.ravel()))
        values = smooth_noise.ravel()
        interpolated_noise = griddata(points, values, (x, y), method='linear', fill_value=0)
        
        chlorophyll_data = base_field + interpolated_noise
        
        # 确保数值在合理范围内
        chlorophyll_data = np.clip(chlorophyll_data, 2.0, 120.0)
        
        return chlorophyll_data
    
    def generate_normal_parameter(self, coords, param_name, timestep, total_timesteps):
        """生成其他正常参数的数据"""
        params = self.normal_params[param_name]
        x, y = coords[:, 0], coords[:, 1]
        
        # 基础空间模式
        base_values = np.random.normal(params['mean'], params['std'], len(coords))
        
        # 添加轻微的时间变化
        time_factor = 0.05 * np.sin(2 * np.pi * timestep / total_timesteps)
        base_values += time_factor * params['std']
        
        # 添加空间相关性
        grid_size = 30
        xi = np.linspace(x.min(), x.max(), grid_size)
        yi = np.linspace(y.min(), y.max(), grid_size)
        xi_grid, yi_grid = np.meshgrid(xi, yi)
        
        noise = np.random.normal(0, params['std'] * 0.3, (grid_size, grid_size))
        smooth_noise = gaussian_filter(noise, sigma=2.0)
        
        points = np.column_stack((xi_grid.ravel(), yi_grid.ravel()))
        values = smooth_noise.ravel()
        spatial_variation = griddata(points, values, (x, y), method='linear', fill_value=0)
        
        final_data = base_values + spatial_variation
        return np.clip(final_data, params['min'], params['max'])
    
    def generate_emergency_dataset(self, node_coords, timesteps=30):
        """
        生成应急数据集
        
        Args:
            node_coords: 节点坐标
            timesteps: 时间步数
        """
        num_nodes = len(node_coords)
        all_timesteps_data = []
        bio_model = PearlMusselModel()
        
        print(f"正在生成 {timesteps} 个时间步的叶绿素爆发应急数据...")
        
        for t in range(timesteps):
            # 生成叶绿素数据（爆发模式）
            chlorophyll = self.generate_chlorophyll_outbreak(node_coords, t, timesteps)
            
            # 生成其他正常参数
            temperature = self.generate_normal_parameter(node_coords, 'temperature', t, timesteps)
            ph = self.generate_normal_parameter(node_coords, 'ph', t, timesteps)
            conductivity = self.generate_normal_parameter(node_coords, 'conductivity', t, timesteps)
            
            # 计算健康度（叶绿素过高会导致健康度下降）
            r = bio_model.ideal_growth_rate(temperature, chlorophyll)
            sigma_total = bio_model.total_stress(ph, conductivity)
            
            # 添加叶绿素过量的额外压力
            chlorophyll_stress = np.where(chlorophyll > 30, 
                                        0.02 * (chlorophyll - 30), 0)  # 超过30μg/L开始产生压力
            sigma_total += chlorophyll_stress
            
            with np.errstate(divide='ignore', invalid='ignore'):
                u_ss = 1.0 * (1 - sigma_total / r)
                u_ss[r <= 0] = 0
            health_index = np.clip(u_ss, 0, 1)
            
            # 组合所有特征
            timestep_features = np.stack([
                temperature, ph, conductivity, chlorophyll, health_index
            ], axis=-1)
            
            all_timesteps_data.append(timestep_features)
            print(f"  > 已生成时间步 {t+1}/{timesteps} - 叶绿素均值: {np.mean(chlorophyll):.2f} μg/L")
        
        return np.stack(all_timesteps_data, axis=1)  # Shape: (N, T, F)

def create_emergency_dataset():
    """创建应急数据集主函数"""
    print("=== 创建叶绿素爆发应急数据集 ===")
    
    # 加载网格
    mesh_path = 'data/mesh/lake_box_mesh_detailed.msh'
    node_coords, elements = load_msh_data(mesh_path)
    if node_coords is None:
        raise FileNotFoundError("网格文件加载失败！")
    
    print(f"✅ 成功加载网格: {len(node_coords)} 个节点")
    
    # 生成应急数据集
    generator = EmergencyDataGenerator()
    emergency_data = generator.generate_emergency_dataset(node_coords, timesteps=30)
    
    # 保存数据集
    save_dir = 'data/emergency'
    os.makedirs(save_dir, exist_ok=True)
    save_path = os.path.join(save_dir, 'chlorophyll_outbreak_dataset.npz')
    
    np.savez(save_path, 
             timeseries=emergency_data, 
             node_coords=node_coords,
             description="Chlorophyll outbreak emergency dataset - 30 timesteps showing gradual chlorophyll increase leading to health decline")
    
    print(f"✅ 应急数据集已保存: {save_path}")
    print(f"   数据形状: {emergency_data.shape}")
    print(f"   叶绿素浓度范围: {np.min(emergency_data[:,:,3]):.2f} - {np.max(emergency_data[:,:,3]):.2f} μg/L")
    print(f"   健康度范围: {np.min(emergency_data[:,:,4]):.3f} - {np.max(emergency_data[:,:,4]):.3f}")
    
    return emergency_data, node_coords, save_path

if __name__ == '__main__':
    emergency_data, node_coords, save_path = create_emergency_dataset()
