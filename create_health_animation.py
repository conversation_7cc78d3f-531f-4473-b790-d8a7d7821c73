#!/usr/bin/env python3
"""
创建健康度动态变化动画
展示从绿色（健康）到红色（不健康）的动态过程
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.tri as mtri
from matplotlib.colors import LinearSegmentedColormap
import os
import sys
from PIL import Image

# 添加项目根目录到Python路径
sys.path.append(os.getcwd())
from shared_utils.mesh_loader import load_msh_data

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class HealthAnimationCreator:
    """健康度动画创建器"""
    
    def __init__(self, node_coords, elements):
        self.node_coords = node_coords
        self.elements = elements
        self.x = node_coords[:, 0]
        self.y = node_coords[:, 1]
        
        # 创建三角化
        self.triang = mtri.Triangulation(self.x, self.y, elements[:, :3] - 1)
        
        # 创建自定义颜色映射：红色（不健康）到绿色（健康）
        colors = ['#FF0000', '#FF4500', '#FF8C00', '#FFD700', '#ADFF2F', '#00FF00']
        self.health_cmap = LinearSegmentedColormap.from_list('health', colors, N=256)
        
        # 定义污染源
        self.pollution_sources = [
            {'center': (np.mean(self.x) + 0.3 * (self.x.max() - self.x.min()), 
                       np.mean(self.y) + 0.2 * (self.y.max() - self.y.min())), 
             'intensity': 0.8, 'spread_rate': 0.15},
            {'center': (np.mean(self.x) - 0.2 * (self.x.max() - self.x.min()), 
                       np.mean(self.y) - 0.3 * (self.y.max() - self.y.min())), 
             'intensity': 0.6, 'spread_rate': 0.12},
            {'center': (np.mean(self.x) + 0.1 * (self.x.max() - self.x.min()), 
                       np.mean(self.y) - 0.1 * (self.y.max() - self.y.min())), 
             'intensity': 0.7, 'spread_rate': 0.18}
        ]
        
        # 计算域的特征尺寸
        self.domain_size = np.sqrt((self.x.max() - self.x.min())**2 + (self.y.max() - self.y.min())**2)
    
    def calculate_health_at_time(self, t, total_time=30):
        """计算时间t时的健康度分布"""
        health = np.ones(len(self.node_coords))  # 初始全部健康
        
        # 时间进展比例
        time_progress = t / total_time
        
        for source in self.pollution_sources:
            center_x, center_y = source['center']
            intensity = source['intensity']
            spread_rate = source['spread_rate']
            
            # 计算距离
            distances = np.sqrt((self.x - center_x)**2 + (self.y - center_y)**2)
            normalized_distances = distances / self.domain_size
            
            # 污染扩散模型
            spread_radius = max(spread_rate * time_progress, 0.01)  # 避免除零
            pollution_effect = intensity * np.exp(-normalized_distances**2 / (2 * spread_radius**2))
            
            # 时间衰减因子
            time_factor = 1 - np.exp(-3 * time_progress)
            
            # 应用污染效应
            health -= pollution_effect * time_factor
        
        # 添加随机噪声
        np.random.seed(42 + t)  # 固定种子确保可重复性
        noise = np.random.normal(0, 0.02, len(health))
        health += noise
        
        # 确保健康度在[0, 1]范围内
        health = np.clip(health, 0.0, 1.0)
        
        return health
    
    def create_animation_frames(self, save_dir, total_frames=30):
        """创建动画帧"""
        os.makedirs(save_dir, exist_ok=True)
        frames_dir = os.path.join(save_dir, 'animation_frames')
        os.makedirs(frames_dir, exist_ok=True)
        
        print(f"正在生成 {total_frames} 帧动画...")
        
        frame_paths = []
        
        for frame in range(total_frames):
            if frame % 5 == 0:
                print(f"  生成第 {frame+1}/{total_frames} 帧...")
            
            # 创建图形
            fig, ax = plt.subplots(1, 1, figsize=(12, 10))
            
            # 计算当前时间的健康度
            health = self.calculate_health_at_time(frame, total_frames)
            
            # 绘制等高线图
            tcf = ax.tricontourf(self.triang, health, levels=50, cmap=self.health_cmap, vmin=0, vmax=1)
            
            # 标记污染源
            for j, source in enumerate(self.pollution_sources):
                center_x, center_y = source['center']
                ax.plot(center_x, center_y, 'k*', markersize=20, markeredgecolor='white', 
                       markeredgewidth=3, label=f'污染源{j+1}' if frame == 0 else "")
            
            ax.set_xlabel('X坐标 (m)', fontsize=12)
            ax.set_ylabel('Y坐标 (m)', fontsize=12)
            ax.set_aspect('equal')
            
            # 添加颜色条
            cbar = plt.colorbar(tcf, ax=ax, shrink=0.8)
            cbar.set_label('健康度指数\n(红色=不健康, 绿色=健康)', fontsize=11)
            
            # 计算统计信息
            mean_health = np.mean(health)
            healthy_ratio = np.sum(health > 0.7) / len(health) * 100
            unhealthy_ratio = np.sum(health < 0.3) / len(health) * 100
            
            # 设置标题
            title = f'珍珠养殖水域健康度动态变化\n第 {frame} 天 - 平均健康度: {mean_health:.3f}'
            ax.set_title(title, fontsize=14, fontweight='bold', pad=20)
            
            # 添加状态信息
            status_text = f'健康区域: {healthy_ratio:.1f}%\n不健康区域: {unhealthy_ratio:.1f}%'
            
            # 根据整体健康状况设置文本颜色
            if mean_health > 0.7:
                text_color = 'green'
                status_bg = 'lightgreen'
            elif mean_health > 0.4:
                text_color = 'orange'
                status_bg = 'lightyellow'
            else:
                text_color = 'red'
                status_bg = 'lightcoral'
            
            ax.text(0.02, 0.98, status_text, transform=ax.transAxes, 
                   verticalalignment='top', fontsize=12, color=text_color, fontweight='bold',
                   bbox=dict(boxstyle='round', facecolor=status_bg, alpha=0.8))
            
            # 添加时间进度条
            progress = frame / (total_frames - 1)
            progress_bar_width = 0.3
            progress_bar_x = 0.65
            progress_bar_y = 0.02
            
            # 绘制进度条背景
            ax.add_patch(plt.Rectangle((progress_bar_x, progress_bar_y), progress_bar_width, 0.03, 
                                     transform=ax.transAxes, facecolor='lightgray', edgecolor='black'))
            
            # 绘制进度条填充
            ax.add_patch(plt.Rectangle((progress_bar_x, progress_bar_y), progress_bar_width * progress, 0.03, 
                                     transform=ax.transAxes, facecolor='blue', alpha=0.7))
            
            # 进度条标签
            ax.text(progress_bar_x + progress_bar_width/2, progress_bar_y + 0.015, 
                   f'进度: {progress*100:.0f}%', transform=ax.transAxes, 
                   ha='center', va='center', fontsize=10, fontweight='bold')
            
            if frame == 0:
                ax.legend(loc='upper right')
            
            plt.tight_layout()
            
            # 保存帧
            frame_path = os.path.join(frames_dir, f'frame_{frame:03d}.png')
            plt.savefig(frame_path, dpi=150, bbox_inches='tight', facecolor='white')
            frame_paths.append(frame_path)
            plt.close()
        
        print(f"✅ 动画帧已保存到: {frames_dir}")
        return frame_paths
    
    def create_gif_animation(self, frame_paths, save_dir, duration=500):
        """创建GIF动画"""
        gif_path = os.path.join(save_dir, 'health_degradation_animation.gif')
        
        print("正在创建GIF动画...")
        
        # 加载所有帧
        images = []
        for frame_path in frame_paths:
            img = Image.open(frame_path)
            images.append(img)
        
        # 保存为GIF
        images[0].save(
            gif_path,
            save_all=True,
            append_images=images[1:],
            duration=duration,
            loop=0,
            optimize=True
        )
        
        print(f"✅ GIF动画已保存: {gif_path}")
        return gif_path
    
    def create_key_moments_comparison(self, save_dir):
        """创建关键时刻对比图"""
        os.makedirs(save_dir, exist_ok=True)
        
        # 选择关键时刻
        key_moments = [0, 10, 20, 29]
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('健康度退化关键时刻对比\n(绿色→红色 表示 健康→不健康)', fontsize=16, fontweight='bold')
        
        for i, t in enumerate(key_moments):
            ax = axes[i//2, i%2]
            
            health = self.calculate_health_at_time(t, 30)
            
            # 绘制等高线图
            tcf = ax.tricontourf(self.triang, health, levels=50, cmap=self.health_cmap, vmin=0, vmax=1)
            
            # 标记污染源
            for j, source in enumerate(self.pollution_sources):
                center_x, center_y = source['center']
                ax.plot(center_x, center_y, 'k*', markersize=15, markeredgecolor='white', 
                       markeredgewidth=2)
            
            ax.set_xlabel('X坐标 (m)')
            ax.set_ylabel('Y坐标 (m)')
            ax.set_aspect('equal')
            
            # 计算统计信息
            mean_health = np.mean(health)
            healthy_ratio = np.sum(health > 0.7) / len(health) * 100
            unhealthy_ratio = np.sum(health < 0.3) / len(health) * 100
            
            ax.set_title(f'第{t}天\n平均健康度: {mean_health:.3f}\n健康区域: {healthy_ratio:.1f}%')
            
            # 添加颜色条
            cbar = plt.colorbar(tcf, ax=ax)
            cbar.set_label('健康度')
            
            # 添加阶段标签
            if t == 0:
                stage_label = "初始状态\n(完全健康)"
                label_color = 'green'
            elif t <= 10:
                stage_label = "早期污染\n(轻微影响)"
                label_color = 'orange'
            elif t <= 20:
                stage_label = "中期污染\n(明显退化)"
                label_color = 'red'
            else:
                stage_label = "严重污染\n(大面积退化)"
                label_color = 'darkred'
            
            ax.text(0.02, 0.02, stage_label, transform=ax.transAxes, 
                   fontsize=11, fontweight='bold', color=label_color,
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'key_moments_comparison.png'), dpi=300, bbox_inches='tight')
        print(f"✅ 关键时刻对比图已保存: {os.path.join(save_dir, 'key_moments_comparison.png')}")
        plt.close()

def main():
    """主函数"""
    print("=== 健康度动态变化动画创建器 ===")
    
    # 加载网格数据
    mesh_path = 'data/mesh/lake_box_mesh_detailed.msh'
    node_coords, elements = load_msh_data(mesh_path)
    if node_coords is None:
        print("❌ 无法加载网格文件")
        return
    
    print(f"✅ 成功加载网格: {len(node_coords)} 个节点")
    
    # 初始化动画创建器
    animator = HealthAnimationCreator(node_coords, elements)
    save_dir = 'results/health_animation'
    
    # 1. 创建关键时刻对比
    print("1. 生成关键时刻对比图...")
    animator.create_key_moments_comparison(save_dir)
    
    # 2. 创建动画帧
    print("2. 生成动画帧...")
    frame_paths = animator.create_animation_frames(save_dir, total_frames=30)
    
    # 3. 创建GIF动画
    print("3. 创建GIF动画...")
    gif_path = animator.create_gif_animation(frame_paths, save_dir, duration=400)
    
    print(f"\n✅ 所有动画创建完成！")
    print(f"📁 结果保存在: {save_dir}")
    print(f"🎬 GIF动画: {gif_path}")
    print(f"🎨 颜色含义: 绿色=健康, 红色=不健康")

if __name__ == '__main__':
    main()
