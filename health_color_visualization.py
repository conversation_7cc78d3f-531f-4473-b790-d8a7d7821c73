#!/usr/bin/env python3
"""
健康度颜色可视化 - 简化版
展示健康度从绿色（健康）到红色（不健康）的颜色变化
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.tri as mtri
from matplotlib.colors import LinearSegmentedColormap
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.getcwd())
from shared_utils.mesh_loader import load_msh_data

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class HealthColorVisualizer:
    """健康度颜色可视化器"""
    
    def __init__(self, node_coords, elements):
        self.node_coords = node_coords
        self.elements = elements
        self.x = node_coords[:, 0]
        self.y = node_coords[:, 1]
        
        # 创建三角化
        self.triang = mtri.Triangulation(self.x, self.y, elements[:, :3] - 1)
        
        # 创建自定义颜色映射：红色（不健康）到绿色（健康）
        # 健康度从0到1：红色→橙色→黄色→黄绿色→绿色
        colors = ['#FF0000', '#FF4500', '#FF8C00', '#FFD700', '#ADFF2F', '#00FF00']
        self.health_cmap = LinearSegmentedColormap.from_list('health', colors, N=256)
        
        # 定义污染源
        self.pollution_sources = [
            {'center': (np.mean(self.x) + 0.3 * (self.x.max() - self.x.min()), 
                       np.mean(self.y) + 0.2 * (self.y.max() - self.y.min())), 
             'intensity': 0.8, 'spread_rate': 0.15},
            {'center': (np.mean(self.x) - 0.2 * (self.x.max() - self.x.min()), 
                       np.mean(self.y) - 0.3 * (self.y.max() - self.y.min())), 
             'intensity': 0.6, 'spread_rate': 0.12},
            {'center': (np.mean(self.x) + 0.1 * (self.x.max() - self.x.min()), 
                       np.mean(self.y) - 0.1 * (self.y.max() - self.y.min())), 
             'intensity': 0.7, 'spread_rate': 0.18}
        ]
        
        # 计算域的特征尺寸
        self.domain_size = np.sqrt((self.x.max() - self.x.min())**2 + (self.y.max() - self.y.min())**2)
    
    def calculate_health_at_time(self, t, total_time=50):
        """计算时间t时的健康度分布"""
        health = np.ones(len(self.node_coords))  # 初始全部健康
        
        # 时间进展比例
        time_progress = t / total_time
        
        for source in self.pollution_sources:
            center_x, center_y = source['center']
            intensity = source['intensity']
            spread_rate = source['spread_rate']
            
            # 计算距离
            distances = np.sqrt((self.x - center_x)**2 + (self.y - center_y)**2)
            normalized_distances = distances / self.domain_size
            
            # 污染扩散模型
            spread_radius = max(spread_rate * time_progress, 0.01)  # 避免除零
            pollution_effect = intensity * np.exp(-normalized_distances**2 / (2 * spread_radius**2))
            
            # 时间衰减因子
            time_factor = 1 - np.exp(-3 * time_progress)
            
            # 应用污染效应
            health -= pollution_effect * time_factor
        
        # 添加随机噪声
        noise = np.random.normal(0, 0.02, len(health))
        health += noise
        
        # 确保健康度在[0, 1]范围内
        health = np.clip(health, 0.0, 1.0)
        
        return health
    
    def create_color_progression(self, save_dir):
        """创建颜色变化进程图"""
        os.makedirs(save_dir, exist_ok=True)
        
        # 选择关键时间点
        time_points = [0, 15, 30, 45]
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('珍珠养殖水域健康度颜色变化进程\n(绿色=健康, 红色=不健康)', fontsize=16, fontweight='bold')
        
        for i, t in enumerate(time_points):
            ax = axes[i//2, i%2]
            
            health = self.calculate_health_at_time(t)
            
            # 绘制等高线图，使用自定义颜色映射
            tcf = ax.tricontourf(self.triang, health, levels=50, cmap=self.health_cmap, vmin=0, vmax=1)
            
            # 标记污染源
            for j, source in enumerate(self.pollution_sources):
                center_x, center_y = source['center']
                ax.plot(center_x, center_y, 'k*', markersize=15, markeredgecolor='white', 
                       markeredgewidth=2, label=f'污染源{j+1}' if i == 0 else "")
            
            ax.set_xlabel('X坐标 (m)')
            ax.set_ylabel('Y坐标 (m)')
            ax.set_title(f'第{t}天 - 平均健康度: {np.mean(health):.3f}')
            ax.set_aspect('equal')
            
            # 添加颜色条
            cbar = plt.colorbar(tcf, ax=ax)
            cbar.set_label('健康度指数\n(0=不健康/红色, 1=健康/绿色)')
            
            # 添加统计信息
            healthy_ratio = np.sum(health > 0.7) / len(health) * 100
            unhealthy_ratio = np.sum(health < 0.3) / len(health) * 100
            
            stats_text = f'健康区域: {healthy_ratio:.1f}%\n不健康区域: {unhealthy_ratio:.1f}%'
            
            # 根据整体健康状况设置文本颜色
            if np.mean(health) > 0.7:
                text_color = 'green'
            elif np.mean(health) > 0.4:
                text_color = 'orange'
            else:
                text_color = 'red'
            
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                   verticalalignment='top', fontsize=11, color=text_color, fontweight='bold',
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
            
            if i == 0:
                ax.legend(loc='upper right')
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'health_color_progression.png'), dpi=300, bbox_inches='tight')
        print(f"✅ 健康度颜色变化图已保存: {os.path.join(save_dir, 'health_color_progression.png')}")
        plt.close()
    
    def create_colorbar_demo(self, save_dir):
        """创建颜色条示例"""
        os.makedirs(save_dir, exist_ok=True)
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        fig.suptitle('健康度颜色映射说明', fontsize=16, fontweight='bold')
        
        # 左图：颜色条示例
        health_values = np.linspace(0, 1, 100).reshape(10, 10)
        im1 = ax1.imshow(health_values, cmap=self.health_cmap, aspect='auto')
        ax1.set_title('健康度颜色映射')
        ax1.set_xlabel('健康度数值')
        ax1.set_ylabel('空间位置')
        
        # 自定义刻度标签
        ax1.set_xticks([0, 25, 50, 75, 99])
        ax1.set_xticklabels(['0.0\n(完全不健康)', '0.25\n(严重)', '0.5\n(中等)', '0.75\n(良好)', '1.0\n(完全健康)'])
        ax1.set_yticks([])
        
        cbar1 = plt.colorbar(im1, ax=ax1)
        cbar1.set_label('健康度指数')
        
        # 右图：颜色与健康状态对应关系
        ax2.axis('off')
        
        # 创建颜色示例
        health_levels = [0.0, 0.2, 0.4, 0.6, 0.8, 1.0]
        descriptions = ['完全不健康', '严重污染', '中度污染', '轻度污染', '基本健康', '完全健康']
        colors_rgb = [self.health_cmap(h) for h in health_levels]
        
        y_positions = np.linspace(0.8, 0.2, len(health_levels))
        
        for i, (health, desc, color) in enumerate(zip(health_levels, descriptions, colors_rgb)):
            # 绘制颜色块
            rect = plt.Rectangle((0.1, y_positions[i]-0.03), 0.15, 0.06, 
                               facecolor=color, edgecolor='black', linewidth=1)
            ax2.add_patch(rect)
            
            # 添加文字说明
            ax2.text(0.3, y_positions[i], f'健康度 {health:.1f}: {desc}', 
                    fontsize=14, verticalalignment='center', fontweight='bold')
        
        ax2.set_xlim(0, 1)
        ax2.set_ylim(0, 1)
        ax2.set_title('健康度等级说明', fontsize=14, fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'colorbar_explanation.png'), dpi=300, bbox_inches='tight')
        print(f"✅ 颜色条说明图已保存: {os.path.join(save_dir, 'colorbar_explanation.png')}")
        plt.close()
    
    def create_health_trend_analysis(self, save_dir):
        """创建健康度趋势分析"""
        os.makedirs(save_dir, exist_ok=True)
        
        # 计算50天的健康度变化
        time_points = np.arange(50)
        mean_health_data = []
        healthy_ratio_data = []
        unhealthy_ratio_data = []
        
        for t in time_points:
            health = self.calculate_health_at_time(t)
            mean_health_data.append(np.mean(health))
            healthy_ratio_data.append(np.sum(health > 0.7) / len(health) * 100)
            unhealthy_ratio_data.append(np.sum(health < 0.3) / len(health) * 100)
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
        fig.suptitle('健康度时间趋势分析', fontsize=16, fontweight='bold')
        
        # 上图：平均健康度变化
        line = ax1.plot(time_points, mean_health_data, linewidth=4)
        
        # 根据健康度值设置线条颜色
        for i in range(len(time_points)-1):
            health_val = mean_health_data[i]
            color = self.health_cmap(health_val)
            ax1.plot([time_points[i], time_points[i+1]], 
                    [mean_health_data[i], mean_health_data[i+1]], 
                    color=color, linewidth=4)
        
        ax1.fill_between(time_points, mean_health_data, alpha=0.3, color='gray')
        ax1.axhline(y=0.7, color='green', linestyle='--', linewidth=2, label='健康阈值 (0.7)')
        ax1.axhline(y=0.3, color='red', linestyle='--', linewidth=2, label='危险阈值 (0.3)')
        
        ax1.set_xlabel('时间 (天)')
        ax1.set_ylabel('平均健康度')
        ax1.set_title('平均健康度时间变化 (线条颜色表示健康状态)')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        ax1.set_ylim(0, 1)
        
        # 下图：健康和不健康区域比例
        ax2.fill_between(time_points, healthy_ratio_data, alpha=0.7, color='green', label='健康区域 (>70%)')
        ax2.fill_between(time_points, unhealthy_ratio_data, alpha=0.7, color='red', label='不健康区域 (<30%)')
        
        ax2.set_xlabel('时间 (天)')
        ax2.set_ylabel('区域比例 (%)')
        ax2.set_title('健康/不健康区域比例变化')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        ax2.set_ylim(0, 100)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'health_trend_analysis.png'), dpi=300, bbox_inches='tight')
        print(f"✅ 健康度趋势分析图已保存: {os.path.join(save_dir, 'health_trend_analysis.png')}")
        plt.close()

def main():
    """主函数"""
    print("=== 健康度颜色可视化 ===")
    
    # 加载网格数据
    mesh_path = 'data/mesh/lake_box_mesh_detailed.msh'
    node_coords, elements = load_msh_data(mesh_path)
    if node_coords is None:
        print("❌ 无法加载网格文件")
        return
    
    print(f"✅ 成功加载网格: {len(node_coords)} 个节点")
    
    # 初始化可视化器
    visualizer = HealthColorVisualizer(node_coords, elements)
    save_dir = 'results/health_color_visualization'
    
    # 1. 创建颜色变化进程图
    print("1. 生成健康度颜色变化进程图...")
    visualizer.create_color_progression(save_dir)
    
    # 2. 创建颜色条说明
    print("2. 生成颜色条说明图...")
    visualizer.create_colorbar_demo(save_dir)
    
    # 3. 创建趋势分析
    print("3. 生成健康度趋势分析...")
    visualizer.create_health_trend_analysis(save_dir)
    
    print(f"\n✅ 所有可视化完成！结果保存在: {save_dir}")
    print("🎨 颜色映射: 红色=不健康, 绿色=健康")

if __name__ == '__main__':
    main()
