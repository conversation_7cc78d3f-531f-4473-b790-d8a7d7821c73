#!/usr/bin/env python3
"""
基于数学模型的叶绿素爆发可视化
详细展示Sigmoid生长函数、空间热点效应和时空演化过程
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.tri as mtri
from matplotlib.animation import FuncAnimation
from scipy.interpolate import griddata
from scipy.ndimage import gaussian_filter
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.getcwd())
from shared_utils.mesh_loader import load_msh_data

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class MathematicalModelVisualizer:
    """基于数学模型的可视化器"""
    
    def __init__(self):
        # 叶绿素爆发的数学参数
        self.params = {
            'initial_mean': 8.0,      # 初始平均浓度 (μg/L)
            'peak_mean': 85.0,        # 爆发峰值 (μg/L)
            'k': 10,                  # Sigmoid陡峭度参数
            'x0': 0.6,                # Sigmoid拐点位置
            'hotspot_intensity': 2.5, # 热点强度倍数
            'decay_rate': 5.0,        # 空间衰减率
            'noise_factor': 0.2       # 噪声因子
        }
        
        # 热点位置（相对坐标）
        self.hotspot_positions = [
            (0.3, 0.2),   # 右上热点
            (-0.2, -0.3), # 左下热点
            (0.1, -0.1)   # 中心偏右热点
        ]
    
    def sigmoid_function(self, t):
        """Sigmoid生长函数"""
        return 1 / (1 + np.exp(-self.params['k'] * (t - self.params['x0'])))
    
    def chlorophyll_concentration(self, t):
        """计算给定时间的叶绿素基础浓度"""
        sigmoid_factor = self.sigmoid_function(t)
        return (self.params['initial_mean'] + 
                (self.params['peak_mean'] - self.params['initial_mean']) * sigmoid_factor)
    
    def spatial_hotspot_effect(self, coords, t, domain_size):
        """计算空间热点效应"""
        x, y = coords[:, 0], coords[:, 1]
        base_concentration = self.chlorophyll_concentration(t)
        sigmoid_factor = self.sigmoid_function(t)
        
        # 初始化基础浓度场
        concentration_field = np.full(len(coords), base_concentration)
        
        # 添加热点效应
        for rel_x, rel_y in self.hotspot_positions:
            # 转换相对坐标到实际坐标
            hotspot_x = np.mean(x) + rel_x * domain_size[0]
            hotspot_y = np.mean(y) + rel_y * domain_size[1]
            
            # 计算距离
            distances = np.sqrt((x - hotspot_x)**2 + (y - hotspot_y)**2)
            max_distance = np.sqrt(domain_size[0]**2 + domain_size[1]**2)
            normalized_distances = distances / max_distance
            
            # 热点效应
            hotspot_effect = (self.params['hotspot_intensity'] * base_concentration * 
                            np.exp(-self.params['decay_rate'] * normalized_distances) * sigmoid_factor)
            concentration_field += hotspot_effect
        
        return concentration_field
    
    def add_spatial_noise(self, coords, base_field, correlation_length=6.0):
        """添加空间相关的噪声"""
        x, y = coords[:, 0], coords[:, 1]
        
        # 创建网格
        grid_size = 50
        xi = np.linspace(x.min(), x.max(), grid_size)
        yi = np.linspace(y.min(), y.max(), grid_size)
        xi_grid, yi_grid = np.meshgrid(xi, yi)
        
        # 生成噪声
        noise_std = np.mean(base_field) * self.params['noise_factor']
        noise = np.random.normal(0, noise_std, (grid_size, grid_size))
        
        # 空间平滑
        sigma = correlation_length * grid_size / (x.max() - x.min())
        smooth_noise = gaussian_filter(noise, sigma=sigma)
        
        # 插值到节点位置
        points = np.column_stack((xi_grid.ravel(), yi_grid.ravel()))
        values = smooth_noise.ravel()
        interpolated_noise = griddata(points, values, (x, y), method='linear', fill_value=0)
        
        return base_field + interpolated_noise
    
    def generate_chlorophyll_field(self, coords, t, domain_size):
        """生成完整的叶绿素浓度场"""
        # 基础浓度 + 热点效应
        field_with_hotspots = self.spatial_hotspot_effect(coords, t, domain_size)
        
        # 添加空间噪声
        final_field = self.add_spatial_noise(coords, field_with_hotspots)
        
        # 确保在合理范围内
        return np.clip(final_field, 2.0, 120.0)
    
    def plot_mathematical_components(self, save_dir):
        """可视化数学模型的各个组件"""
        os.makedirs(save_dir, exist_ok=True)
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('叶绿素爆发数学模型组件分析', fontsize=16, fontweight='bold')
        
        # 1. Sigmoid函数
        t_values = np.linspace(0, 1, 100)
        sigmoid_values = self.sigmoid_function(t_values)
        
        ax1.plot(t_values, sigmoid_values, 'b-', linewidth=3, label='Sigmoid函数')
        ax1.axhline(y=0.5, color='gray', linestyle='--', alpha=0.7)
        ax1.axvline(x=self.params['x0'], color='gray', linestyle='--', alpha=0.7)
        ax1.plot(self.params['x0'], 0.5, 'ro', markersize=10, label=f'拐点 ({self.params["x0"]}, 0.5)')
        
        ax1.set_xlabel('时间进展比例')
        ax1.set_ylabel('Sigmoid因子')
        ax1.set_title(f'Sigmoid函数 (k={self.params["k"]}, x₀={self.params["x0"]})')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # 2. 叶绿素浓度时间序列
        chlorophyll_values = [self.chlorophyll_concentration(t) for t in t_values]
        
        ax2.plot(t_values * 30, chlorophyll_values, 'g-', linewidth=3, label='叶绿素浓度')
        ax2.axhline(y=30, color='orange', linestyle='--', linewidth=2, label='危险阈值 (30 μg/L)')
        ax2.axhline(y=self.params['initial_mean'], color='blue', linestyle=':', alpha=0.7, 
                   label=f'初始浓度 ({self.params["initial_mean"]} μg/L)')
        ax2.axhline(y=self.params['peak_mean'], color='red', linestyle=':', alpha=0.7, 
                   label=f'峰值浓度 ({self.params["peak_mean"]} μg/L)')
        
        ax2.set_xlabel('时间 (天)')
        ax2.set_ylabel('叶绿素浓度 (μg/L)')
        ax2.set_title('叶绿素浓度时间演化')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # 3. 空间衰减函数
        distances = np.linspace(0, 1, 100)
        decay_values = np.exp(-self.params['decay_rate'] * distances)
        
        ax3.plot(distances, decay_values, 'r-', linewidth=3, label='空间衰减函数')
        ax3.set_xlabel('归一化距离')
        ax3.set_ylabel('衰减因子')
        ax3.set_title(f'热点空间衰减 (α={self.params["decay_rate"]})')
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        
        # 添加衰减特性说明
        ax3.text(0.5, 0.7, f'衰减函数: exp(-{self.params["decay_rate"]} × d)\n'
                          f'50%衰减距离: {np.log(2)/self.params["decay_rate"]:.3f}\n'
                          f'90%衰减距离: {np.log(10)/self.params["decay_rate"]:.3f}',
                transform=ax3.transAxes, fontsize=10,
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        # 4. 热点效应强度随时间变化
        time_points = np.linspace(0, 1, 100)
        hotspot_intensities = []
        
        for t in time_points:
            base_conc = self.chlorophyll_concentration(t)
            sigmoid_factor = self.sigmoid_function(t)
            # 在距离=0处的热点效应
            intensity = self.params['hotspot_intensity'] * base_conc * sigmoid_factor
            hotspot_intensities.append(intensity)
        
        ax4.plot(time_points * 30, hotspot_intensities, 'm-', linewidth=3, label='热点中心强度')
        ax4.set_xlabel('时间 (天)')
        ax4.set_ylabel('额外叶绿素浓度 (μg/L)')
        ax4.set_title('热点效应强度时间演化')
        ax4.grid(True, alpha=0.3)
        ax4.legend()
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'mathematical_components.png'), dpi=300, bbox_inches='tight')
        print(f"✅ 数学组件分析图已保存: {os.path.join(save_dir, 'mathematical_components.png')}")
        plt.close()
    
    def plot_spatial_evolution(self, node_coords, elements, save_dir, time_points=[0, 0.3, 0.6, 0.9]):
        """可视化空间演化过程"""
        os.makedirs(save_dir, exist_ok=True)
        
        # 计算域大小
        x, y = node_coords[:, 0], node_coords[:, 1]
        domain_size = (x.max() - x.min(), y.max() - y.min())
        
        # 创建三角化
        triang = mtri.Triangulation(x, y, elements[:, :3] - 1)
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('叶绿素空间分布演化过程', fontsize=16, fontweight='bold')
        
        # 计算全局颜色范围
        all_values = []
        for t in time_points:
            field = self.generate_chlorophyll_field(node_coords, t, domain_size)
            all_values.extend(field)
        vmin, vmax = min(all_values), max(all_values)
        
        for i, t in enumerate(time_points):
            ax = axes[i//2, i%2]
            
            # 生成叶绿素场
            chlorophyll_field = self.generate_chlorophyll_field(node_coords, t, domain_size)
            
            # 绘制等高线图
            tcf = ax.tricontourf(triang, chlorophyll_field, levels=50, cmap='YlOrRd', vmin=vmin, vmax=vmax)
            
            # 标记热点位置
            for rel_x, rel_y in self.hotspot_positions:
                hotspot_x = np.mean(x) + rel_x * domain_size[0]
                hotspot_y = np.mean(y) + rel_y * domain_size[1]
                ax.plot(hotspot_x, hotspot_y, 'k*', markersize=15, markeredgecolor='white', markeredgewidth=2)
            
            ax.set_xlabel('X坐标 (m)')
            ax.set_ylabel('Y坐标 (m)')
            ax.set_title(f'时间进展: {t:.1f} (第{t*30:.0f}天)\n'
                        f'平均浓度: {np.mean(chlorophyll_field):.1f} μg/L')
            ax.set_aspect('equal')
            
            # 添加颜色条
            cbar = plt.colorbar(tcf, ax=ax)
            cbar.set_label('叶绿素浓度 (μg/L)')
            
            # 添加统计信息
            stats_text = f'最大值: {np.max(chlorophyll_field):.1f}\n最小值: {np.min(chlorophyll_field):.1f}'
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                   verticalalignment='top', fontsize=10,
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'spatial_evolution.png'), dpi=300, bbox_inches='tight')
        print(f"✅ 空间演化图已保存: {os.path.join(save_dir, 'spatial_evolution.png')}")
        plt.close()
    
    def plot_hotspot_analysis(self, node_coords, save_dir):
        """分析热点效应的空间分布"""
        os.makedirs(save_dir, exist_ok=True)
        
        x, y = node_coords[:, 0], node_coords[:, 1]
        domain_size = (x.max() - x.min(), y.max() - y.min())
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('热点效应空间分析', fontsize=16, fontweight='bold')
        
        # 不同时间点的热点效应
        time_points = [0.2, 0.5, 0.7, 0.9]
        
        for i, t in enumerate(time_points):
            ax = axes[i//2, i%2]
            
            base_conc = self.chlorophyll_concentration(t)
            sigmoid_factor = self.sigmoid_function(t)
            
            # 计算每个热点的单独效应
            total_hotspot_effect = np.zeros(len(node_coords))
            
            for j, (rel_x, rel_y) in enumerate(self.hotspot_positions):
                hotspot_x = np.mean(x) + rel_x * domain_size[0]
                hotspot_y = np.mean(y) + rel_y * domain_size[1]
                
                distances = np.sqrt((x - hotspot_x)**2 + (y - hotspot_y)**2)
                max_distance = np.sqrt(domain_size[0]**2 + domain_size[1]**2)
                normalized_distances = distances / max_distance
                
                hotspot_effect = (self.params['hotspot_intensity'] * base_conc * 
                                np.exp(-self.params['decay_rate'] * normalized_distances) * sigmoid_factor)
                total_hotspot_effect += hotspot_effect
            
            # 创建网格用于平滑显示
            grid_x, grid_y = np.mgrid[x.min():x.max():100j, y.min():y.max():100j]
            grid_effect = griddata((x, y), total_hotspot_effect, (grid_x, grid_y), method='cubic')
            
            im = ax.imshow(grid_effect.T, extent=(x.min(), x.max(), y.min(), y.max()), 
                          origin='lower', cmap='Reds', alpha=0.8)
            
            # 标记热点位置
            for rel_x, rel_y in self.hotspot_positions:
                hotspot_x = np.mean(x) + rel_x * domain_size[0]
                hotspot_y = np.mean(y) + rel_y * domain_size[1]
                ax.plot(hotspot_x, hotspot_y, 'k*', markersize=20, markeredgecolor='white', markeredgewidth=3)
            
            ax.set_xlabel('X坐标 (m)')
            ax.set_ylabel('Y坐标 (m)')
            ax.set_title(f'时间进展: {t:.1f}\n热点效应强度分布')
            ax.set_aspect('equal')
            
            # 添加颜色条
            cbar = plt.colorbar(im, ax=ax)
            cbar.set_label('额外叶绿素浓度 (μg/L)')
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'hotspot_analysis.png'), dpi=300, bbox_inches='tight')
        print(f"✅ 热点分析图已保存: {os.path.join(save_dir, 'hotspot_analysis.png')}")
        plt.close()

    def plot_timeseries_analysis(self, save_dir):
        """绘制详细的时序分析"""
        os.makedirs(save_dir, exist_ok=True)

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('叶绿素爆发时序分析', fontsize=16, fontweight='bold')

        # 时间序列
        time_steps = np.arange(30)
        time_progress = time_steps / 29

        # 1. 基础浓度时序
        base_concentrations = [self.chlorophyll_concentration(t) for t in time_progress]
        ax1.plot(time_steps, base_concentrations, 'b-', linewidth=3, label='基础浓度')
        ax1.axhline(y=30, color='orange', linestyle='--', linewidth=2, label='危险阈值')
        ax1.set_xlabel('时间 (天)')
        ax1.set_ylabel('叶绿素浓度 (μg/L)')
        ax1.set_title('基础浓度时间序列')
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # 2. Sigmoid因子和增长速率
        sigmoid_factors = [self.sigmoid_function(t) for t in time_progress]
        ax2_twin = ax2.twinx()

        line1 = ax2.plot(time_steps, sigmoid_factors, 'g-', linewidth=3, label='Sigmoid因子')

        # 计算增长速率（一阶导数）
        dt = time_progress[1] - time_progress[0]
        growth_rates = np.gradient(base_concentrations, dt)
        line2 = ax2_twin.plot(time_steps, growth_rates, 'r-', linewidth=3, label='增长速率')

        ax2.set_xlabel('时间 (天)')
        ax2.set_ylabel('Sigmoid因子', color='g')
        ax2_twin.set_ylabel('增长速率 (μg/L/天)', color='r')
        ax2.set_title('Sigmoid因子与增长速率')

        # 合并图例
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax2.legend(lines, labels, loc='center right')
        ax2.grid(True, alpha=0.3)

        # 3. 热点效应强度
        hotspot_intensities = []
        for t in time_progress:
            base_conc = self.chlorophyll_concentration(t)
            sigmoid_factor = self.sigmoid_function(t)
            intensity = self.params['hotspot_intensity'] * base_conc * sigmoid_factor
            hotspot_intensities.append(intensity)

        ax3.plot(time_steps, hotspot_intensities, 'm-', linewidth=3, label='热点中心强度')
        ax3.set_xlabel('时间 (天)')
        ax3.set_ylabel('额外叶绿素浓度 (μg/L)')
        ax3.set_title('热点效应强度演化')
        ax3.grid(True, alpha=0.3)
        ax3.legend()

        # 4. 阶段性分析
        stages = ['缓慢增长期', '快速爆发期', '稳定期']
        stage_colors = ['green', 'orange', 'red']
        stage_ranges = [(0, 18), (18, 24), (24, 30)]

        for i, (stage, color, (start, end)) in enumerate(zip(stages, stage_colors, stage_ranges)):
            stage_times = time_steps[start:end]
            stage_concs = base_concentrations[start:end]
            ax4.plot(stage_times, stage_concs, color=color, linewidth=4, label=stage)

        ax4.set_xlabel('时间 (天)')
        ax4.set_ylabel('叶绿素浓度 (μg/L)')
        ax4.set_title('爆发阶段分析')
        ax4.grid(True, alpha=0.3)
        ax4.legend()

        # 添加阶段分界线
        for boundary in [18, 24]:
            ax4.axvline(x=boundary, color='black', linestyle=':', alpha=0.7)

        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'timeseries_analysis.png'), dpi=300, bbox_inches='tight')
        print(f"✅ 时序分析图已保存: {os.path.join(save_dir, 'timeseries_analysis.png')}")
        plt.close()

def main():
    """主函数"""
    print("=== 基于数学模型的叶绿素爆发可视化 ===")

    # 加载网格数据
    mesh_path = 'data/mesh/lake_box_mesh_detailed.msh'
    node_coords, elements = load_msh_data(mesh_path)
    if node_coords is None:
        print("❌ 无法加载网格文件")
        return

    print(f"✅ 成功加载网格: {len(node_coords)} 个节点")

    # 初始化可视化器
    visualizer = MathematicalModelVisualizer()
    save_dir = 'results/mathematical_model_visualization'

    # 1. 数学组件分析
    print("1. 生成数学组件分析图...")
    visualizer.plot_mathematical_components(save_dir)

    # 2. 空间演化过程
    print("2. 生成空间演化过程图...")
    visualizer.plot_spatial_evolution(node_coords, elements, save_dir)

    # 3. 热点效应分析
    print("3. 生成热点效应分析图...")
    visualizer.plot_hotspot_analysis(node_coords, save_dir)

    # 4. 时序分析
    print("4. 生成时序分析图...")
    visualizer.plot_timeseries_analysis(save_dir)

    print(f"\n✅ 所有可视化完成！结果保存在: {save_dir}")

if __name__ == '__main__':
    main()
